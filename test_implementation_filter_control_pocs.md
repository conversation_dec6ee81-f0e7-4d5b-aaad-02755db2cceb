# Test Implementation Plan for `filter_control_pocs` Function

## Overview
This plan details the process of implementing tests for the `filter_control_pocs` function to ensure it works correctly after modularization.

## Test File Location
The test file will be created at `tests/test_filter_control_pocs.py`

## Test File Structure
Based on the existing test structure in the project and the test plan created earlier, the test file will follow this structure:

```python
import unittest
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error
import math
import sys
import os

# Add the Code directory to the path so we can import from data_processing.py
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_processing import filter_control_pocs

class TestFilterControlPocsFunction(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
    
    def test_normal_case(self):
        """Test with standard input data."""
        # Implementation
    
    def test_empty_dataframe(self):
        """Test behavior with empty input."""
        # Implementation
    
    def test_missing_columns(self):
        """Test behavior with missing columns."""
        # Implementation
    
    def test_mismatched_indices(self):
        """Test behavior with mismatched indices."""
        # Implementation
    
    def test_limit_greater_than_controls(self):
        """Test with limit greater than number of control POCs."""
        # Implementation
    
    def test_limit_less_than_controls(self):
        """Test with limit less than number of control POCs."""
        # Implementation
    
    def test_nan_values(self):
        """Test with NaN values in data."""
        # Implementation
    
    def test_single_control_poc(self):
        """Test with single control POC."""
        # Implementation
    
    def test_identical_data(self):
        """Test with identical test and control data."""
        # Implementation

if __name__ == '__main__':
    unittest.main()
```

## Detailed Test Implementations

### 1. Normal Case Test
Create sample DataFrames with realistic data and verify the function works correctly:

```python
def test_normal_case(self):
    """Test with standard input data."""
    # Create test global_ctrl DataFrame
    ctrl_data = {
        'POC_ID': ['1001', '1002', '1003'],
        'col1': [10, 20, 30],
        'col2': [15, 25, 35],
        'col3': [12, 22, 32],
        'col4': [18, 28, 38]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    # Create test global_test DataFrame
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17],
        'col3': [14],
        'col4': [20]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters
    min_index = 1  # Start of baseline period
    activation_on = 3  # Activation date index
    limit = 2  # Limit for number of control POCs
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    self.assertIn('RMSE Value', result.columns)
    self.assertIn('RMSE_Rank', result.columns)
    self.assertLessEqual(len(result), limit)
    # Verify that only POC_IDs from the original control are in the result
    self.assertTrue(all(poc_id in global_ctrl['POC_ID'].tolist() for poc_id in result['POC_ID'].tolist()))
```

### 2. Empty DataFrame Test
Test with empty DataFrames:

```python
def test_empty_dataframe(self):
    """Test behavior with empty input."""
    # Create empty DataFrames
    global_ctrl = pd.DataFrame()
    global_test = pd.DataFrame()
    
    # Set parameters
    min_index = 0
    activation_on = 1
    limit = 5
    
    # Expect an exception or graceful handling
    # Depending on how the function should behave with empty data
    try:
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        # If it doesn't raise an exception, check the result
        self.assertIsInstance(result, pd.DataFrame)
    except Exception as e:
        # If it raises an exception, that's also valid behavior
        # Just make sure it's handled gracefully
        pass
```

### 3. Missing Columns Test
Test with DataFrames missing required columns:

```python
def test_missing_columns(self):
    """Test behavior with missing columns."""
    # Create DataFrames with missing columns
    ctrl_data = {
        'POC_ID': ['1001', '1002']
        # Missing data columns
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001']
        # Missing data columns
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters
    min_index = 1
    activation_on = 2
    limit = 5
    
    # Expect an exception due to missing columns
    with self.assertRaises(Exception):
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
```

### 4. Mismatched Indices Test
Test with mismatched column indices:

```python
def test_mismatched_indices(self):
    """Test behavior with mismatched indices."""
    # Create DataFrames with mismatched indices
    ctrl_data = {
        'POC_ID': ['1001', '1002'],
        'col1': [10, 20],
        'col2': [15, 25]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17],
        'col3': [14]  # Extra column
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters that would cause index issues
    min_index = 1
    activation_on = 4  # This would be out of bounds for global_ctrl
    limit = 5
    
    # Expect an exception due to index out of bounds
    with self.assertRaises(Exception):
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
```

### 5. Limit Greater Than Controls Test
Test with limit greater than number of control POCs:

```python
def test_limit_greater_than_controls(self):
    """Test with limit greater than number of control POCs."""
    # Create test DataFrames
    ctrl_data = {
        'POC_ID': ['1001', '1002', '1003'],
        'col1': [10, 20, 30],
        'col2': [15, 25, 35]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters with limit greater than number of controls
    min_index = 1
    activation_on = 3
    limit = 10  # Greater than number of control POCs (3)
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    # Should return all control POCs since limit is greater than available
    self.assertEqual(len(result), len(global_ctrl))
```

### 6. Limit Less Than Controls Test
Test with limit less than number of control POCs:

```python
def test_limit_less_than_controls(self):
    """Test with limit less than number of control POCs."""
    # Create test DataFrames
    ctrl_data = {
        'POC_ID': ['1001', '1002', '1003', '1004', '1005'],
        'col1': [10, 20, 30, 40, 50],
        'col2': [15, 25, 35, 45, 55]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters with limit less than number of controls
    min_index = 1
    activation_on = 3
    limit = 3  # Less than number of control POCs (5)
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    # Should return only the specified limit number of control POCs
    self.assertEqual(len(result), limit)
```

### 7. NaN Values Test
Test with DataFrames containing NaN values:

```python
def test_nan_values(self):
    """Test with NaN values in data."""
    # Create test DataFrames with NaN values
    ctrl_data = {
        'POC_ID': ['1001', '1002', '1003'],
        'col1': [10, np.nan, 30],
        'col2': [15, 25, np.nan]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters
    min_index = 1
    activation_on = 3
    limit = 5
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    # Check that the function handled NaN values appropriately
```

### 8. Single Control POC Test
Test with only one control POC:

```python
def test_single_control_poc(self):
    """Test with single control POC."""
    # Create test DataFrames with single control POC
    ctrl_data = {
        'POC_ID': ['1001'],
        'col1': [10],
        'col2': [15]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [12],
        'col2': [17]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters
    min_index = 1
    activation_on = 3
    limit = 5
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    self.assertEqual(len(result), 1)
    self.assertEqual(result['POC_ID'].iloc[0], '1001')
```

### 9. Identical Data Test
Test with identical test and control data:

```python
def test_identical_data(self):
    """Test with identical test and control data."""
    # Create test DataFrames with identical data
    ctrl_data = {
        'POC_ID': ['1001', '1002'],
        'col1': [10, 10],
        'col2': [15, 15]
    }
    global_ctrl = pd.DataFrame(ctrl_data)
    
    test_data = {
        'POC_ID': ['2001'],
        'col1': [10],
        'col2': [15]
    }
    global_test = pd.DataFrame(test_data)
    
    # Set parameters
    min_index = 1
    activation_on = 3
    limit = 5
    
    # Call the function
    result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    # Assertions
    self.assertIsInstance(result, pd.DataFrame)
    # With identical data, RMSE should be 0 for all controls
    self.assertTrue(all(result['RMSE Value'] == 0))
```

## Running the Tests
To run the tests, execute the following command from the project root directory:

```bash
python -m unittest tests/test_filter_control_pocs.py
```

Or to run all tests:

```bash
python -m unittest discover tests
```

## Test Verification
After implementing the tests, verify that:
1. All test cases pass
2. Edge cases are handled appropriately
3. The function behaves the same as before modularization
4. No regressions are introduced

## Test Maintenance
When updating the function in the future:
1. Update the corresponding tests if functionality changes
2. Add new test cases for new functionality
3. Ensure all existing tests continue to pass
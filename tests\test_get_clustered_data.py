import unittest
import pandas as pd
import numpy as np
import sys
import os
from unittest.mock import Mock, patch

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

# Import the function to be tested
from analysis import get_clustered_data

class TestGetClusteredData(unittest.TestCase):
    
    def setUp(self):
        """Set up test data"""
        # Create sample DATA DataFrame
        self.sample_data = pd.DataFrame({
            'POC_ID': [1, 2, 3, 4, 5, 6],
            'Test_Control': ['Test', 'Control', 'Control', 'Test', 'Control', 'Test'],
            '2023-01-01': [10, 20, 30, 40, 50, 60],
            '2023-01-08': [15, 25, 35, 45, 55, 65],
            '2023-01-15': [12, 22, 32, 42, 52, 62]
        })
    
    def test_normal_case(self):
        """Test with typical inputs"""
        # Create sample distance matrices
        dist_mat_grp = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]]),
            np.array([[0, 3], [3, 0]])
        ]
        
        # Number of clusters for each group
        num_clusters_grp = [2, 1]
        
        # Date groups (not used in function but needed for signature)
        date_grp = [
            pd.Series(['2023-01-01', '2023-01-01', '2023-01-01']),
            pd.Series(['2023-01-01', '2023-01-01'])
        ]
        
        # POC ID groups
        poc_ids_grp = [
            pd.Series([1, 2, 3]),  # POC IDs for first group
            pd.Series([4, 5])      # POC IDs for second group
        ]
        
        # KMeans models list (output parameter)
        kmeans_grp = []
        
        # Call the function
        result = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, self.sample_data)
        
        # Check that result is a list
        self.assertIsInstance(result, list)
        
        # Check that we have the expected number of groups
        self.assertEqual(len(result), 2)
        
        # Check that each element is a DataFrame
        for group in result:
            self.assertIsInstance(group, pd.DataFrame)
        
        # Check that each DataFrame has a Cluster column
        for group in result:
            self.assertIn('Cluster', group.columns)
        
        # Check that kmeans_grp was populated
        self.assertEqual(len(kmeans_grp), 2)
        
        # Check that the DataFrames contain the expected POC IDs
        # Note: Actual POC IDs in result may differ due to clustering, so we check the structure
        self.assertEqual(len(result[0]), 3)  # First group should have 3 rows
        self.assertEqual(len(result[1]), 2)  # Second group should have 2 rows
    
    def test_single_group(self):
        """Test with a single data group"""
        # Create sample distance matrix for one group
        dist_mat_grp = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]])
        ]
        
        # Number of clusters for the group
        num_clusters_grp = [2]
        
        # Date group
        date_grp = [
            pd.Series(['2023-01-01', '2023-01-01', '2023-01-01'])
        ]
        
        # POC ID group
        poc_ids_grp = [
            pd.Series([1, 2, 3])  # POC IDs for the group
        ]
        
        # KMeans models list (output parameter)
        kmeans_grp = []
        
        # Call the function
        result = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, self.sample_data)
        
        # Check that result is a list with one element
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Check that the element is a DataFrame
        self.assertIsInstance(result[0], pd.DataFrame)
        
        # Check that the DataFrame has a Cluster column
        self.assertIn('Cluster', result[0].columns)
        
        # Check that kmeans_grp was populated
        self.assertEqual(len(kmeans_grp), 1)
        
        # Check that the DataFrame contains the expected number of rows
        self.assertEqual(len(result[0]), 3)
    
    def test_empty_lists(self):
        """Test with empty input lists"""
        # Empty input lists
        dist_mat_grp = []
        num_clusters_grp = []
        date_grp = []
        poc_ids_grp = []
        kmeans_grp = []
        
        # Call the function
        result = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, self.sample_data)
        
        # Check that result is an empty list
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)
        
        # Check that kmeans_grp is still empty
        self.assertEqual(len(kmeans_grp), 0)
    
    def test_mismatched_list_lengths(self):
        """Test with mismatched list lengths"""
        # Create sample distance matrices
        dist_mat_grp = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]]),
            np.array([[0, 3], [3, 0]])
        ]
        
        # Mismatched number of clusters (only 1 element instead of 2)
        num_clusters_grp = [2]
        
        # Date groups
        date_grp = [
            pd.Series(['2023-01-01', '2023-01-01', '2023-01-01']),
            pd.Series(['2023-01-01', '2023-01-01'])
        ]
        
        # POC ID groups
        poc_ids_grp = [
            pd.Series([1, 2, 3]),
            pd.Series([4, 5])
        ]
        
        # KMeans models list (output parameter)
        kmeans_grp = []
        
        # Call the function and expect an IndexError
        with self.assertRaises(IndexError):
            get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, self.sample_data)
    
    def test_empty_data_frame(self):
        """Test with empty DATA DataFrame"""
        # Create sample distance matrices
        dist_mat_grp = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]])
        ]
        
        # Number of clusters for each group
        num_clusters_grp = [2]
        
        # Date groups
        date_grp = [
            pd.Series(['2023-01-01', '2023-01-01', '2023-01-01'])
        ]
        
        # POC ID groups
        poc_ids_grp = [
            pd.Series([1, 2, 3])
        ]
        
        # KMeans models list (output parameter)
        kmeans_grp = []
        
        # Empty DATA DataFrame with same structure
        empty_data = pd.DataFrame(columns=['POC_ID', 'Test_Control', '2023-01-01', '2023-01-08', '2023-01-15'])
        
        # Call the function
        result = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, empty_data)
        
        # Check that result is a list with one element
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Check that the element is a DataFrame
        self.assertIsInstance(result[0], pd.DataFrame)
        
        # Check that the DataFrame has a Cluster column
        self.assertIn('Cluster', result[0].columns)
        
        # Check that the DataFrame is empty
        self.assertEqual(len(result[0]), 0)
        
        # Check that kmeans_grp was populated
        self.assertEqual(len(kmeans_grp), 1)

if __name__ == '__main__':
    unittest.main()
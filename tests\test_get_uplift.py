import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from analysis import get_uplift

class TestGetUplift(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for get_uplift function"""
        # Create sample APT_RESULTS DataFrame
        self.APT_RESULTS = pd.DataFrame({
            'POC_ID': [1001, 1002, 1003],
            'ABI-TEST AVG VOL': [0.0, 0.0, 0.0],
            'ABI-AVG VOL': [0.0, 0.0, 0.0],
            'ABI-%Lift': [0.0, 0.0, 0.0],
            'ABI-Estimated impact': [0.0, 0.0, 0.0],
            'ABI-Test baseline period': [0.0, 0.0, 0.0],
            'ABI-Test analysis period': [0.0, 0.0, 0.0],
            'ABI-Test expected': [0.0, 0.0, 0.0],
            'ABI-%Test performance': [0.0, 0.0, 0.0],
            'ABI-Control baseline period': [0.0, 0.0, 0.0],
            'ABI-Control analysis period': [0.0, 0.0, 0.0],
            'ABI-%Control performance': [0.0, 0.0, 0.0],
            'ABI-Baseline period # weeks with data': [0, 0, 0],
            'ABI-Analysis period # weeks with data': [0, 0, 0],
            'ABI-Control count': [0, 0, 0],
            'ABI-Control Outliers': [0, 0, 0],
            'ABI-Goodness of fit score': [0.0, 0.0, 0.0],
            'Campaign': ['', '', ''],
            'Retailer': ['', '', ''],
            'SKU': [0, 0, 0]
        })
    
    def test_normal_operation(self):
        """Test get_uplift with normal data"""
        # Sample test and control data
        desc_test = pd.Series([10, 15, 20, 25, 30, 35, 40, 45])
        desc_ctrl = pd.Series([8, 12, 16, 20, 24, 28, 32, 36])
        
        min_index = 0
        activation_on = 2
        activation_end = 5
        test_poc = 1001
        ctrl_pocs = [2001, 2002, 2003]
        ctrl_outliers = 0
        RESTRICT_BASELINE_TO = 12
        campaign = "Test Campaign"
        retailer = "Test Retailer"
        sku = 12345
        
        # Make a copy of APT_RESULTS for this test
        APT_RESULTS_copy = self.APT_RESULTS.copy()
        
        # Call get_uplift function
        result = get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end,
                           test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO,
                           APT_RESULTS_copy, campaign, retailer, sku)
        
        # Check that result is a list with 6 elements
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 6)
        
        # Check that APT_RESULTS was updated
        updated_row = APT_RESULTS_copy[APT_RESULTS_copy['POC_ID'] == test_poc]
        self.assertFalse(updated_row.empty)
        
        # Check some specific values
        self.assertEqual(updated_row['Campaign'].iloc[0], campaign)
        self.assertEqual(updated_row['Retailer'].iloc[0], retailer)
        self.assertEqual(updated_row['SKU'].iloc[0], sku)
    
    def test_all_zeros(self):
        """Test get_uplift with all zero values"""
        # Sample test and control data with all zeros (same length)
        desc_test = pd.Series([0, 0, 0, 0, 0, 0, 0])
        desc_ctrl = pd.Series([0, 0, 0, 0, 0, 0, 0, 0])  # Same length as desc_test
        
        min_index = 0
        activation_on = 2
        activation_end = 5
        test_poc = 1002
        ctrl_pocs = [2001, 2002, 2003]
        ctrl_outliers = 0
        RESTRICT_BASELINE_TO = 12
        campaign = "Zero Campaign"
        retailer = "Zero Retailer"
        sku = 54321
        
        # Make a copy of APT_RESULTS for this test
        APT_RESULTS_copy = self.APT_RESULTS.copy()
        
        # Call get_uplift function
        result = get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end,
                           test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO,
                           APT_RESULTS_copy, campaign, retailer, sku)
        
        # Check that result is a list with 6 elements
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 6)
        
        # Check that APT_RESULTS was updated
        updated_row = APT_RESULTS_copy[APT_RESULTS_copy['POC_ID'] == test_poc]
        self.assertFalse(updated_row.empty)
    
    def test_mismatched_indices(self):
        """Test get_uplift with mismatched indices"""
        # Sample test and control data with different lengths
        desc_test = pd.Series([10, 15, 20, 25, 30])
        desc_ctrl = pd.Series([8, 12, 16, 20, 24, 28, 32])
        
        min_index = 0
        activation_on = 1
        activation_end = 3
        test_poc = 1003
        ctrl_pocs = [2001, 2002, 2003]
        ctrl_outliers = 0
        RESTRICT_BASELINE_TO = 12
        campaign = "Mismatched Campaign"
        retailer = "Mismatched Retailer"
        sku = 98765
        
        # Make a copy of APT_RESULTS for this test
        APT_RESULTS_copy = self.APT_RESULTS.copy()
        
        # Call get_uplift function
        result = get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end,
                           test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO,
                           APT_RESULTS_copy, campaign, retailer, sku)
        
        # Since the lengths are different, the function should print an error and return early
        # We can't easily test the print output, but we can verify the function doesn't crash
        # and that APT_RESULTS is not updated for this POC_ID
        updated_row = APT_RESULTS_copy[APT_RESULTS_copy['POC_ID'] == test_poc]
        # With mismatched lengths, the row should not be updated with new values
        # but should still exist with original values
        self.assertFalse(updated_row.empty)
    
    def test_empty_data(self):
        """Test get_uplift with empty data"""
        # Sample test and control data with empty series (same length)
        desc_test = pd.Series([])
        desc_ctrl = pd.Series([])
        
        min_index = 0
        activation_on = 0
        activation_end = 0
        test_poc = 1001
        ctrl_pocs = []
        ctrl_outliers = 0
        RESTRICT_BASELINE_TO = 12
        campaign = "Empty Campaign"
        retailer = "Empty Retailer"
        sku = 11111
        
        # Make a copy of APT_RESULTS for this test
        APT_RESULTS_copy = self.APT_RESULTS.copy()
        
        # Call get_uplift function
        result = get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end,
                           test_poc, ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO,
                           APT_RESULTS_copy, campaign, retailer, sku)
        
        # Since the data is empty, the function should print an error and return early
        # We can verify the function doesn't crash
        updated_row = APT_RESULTS_copy[APT_RESULTS_copy['POC_ID'] == test_poc]
        self.assertFalse(updated_row.empty)

if __name__ == '__main__':
    unittest.main()
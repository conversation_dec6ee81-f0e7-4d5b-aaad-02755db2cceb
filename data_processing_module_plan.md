# Plan for Creating data_processing.py Module

## Overview
This document outlines the plan for creating the `data_processing.py` module and moving the `create_val_data` function to it.

## Module Content
The `data_processing.py` module should contain the `create_val_data` function with the following implementation:

```python
import os
import pandas as pd


def create_val_data(sku, retailer):
    """
    Read and process SKU data from CSV files for a given retailer.
    
    This function performs the following operations:
    1. Constructs a path to SKU data based on the retailer
    2. Reads a CSV file for the specific SKU
    3. Renames several columns:
       - 'Store_Code' to 'POC SAPID'
       - 'Store Code applied by the retailer' to 'Store_Code'
       - 'Sales_Value' to 'Sum(Value)'
       - 'Sales_Units' to 'Sales_Units'
       - 'WEEK' to 'Date'
    4. Converts the 'Date' column to datetime
    5. Extracts year and month from the date
    6. Creates a 'Periods' column from year and month
    7. Returns the processed dataframe
    
    Args:
        sku (str): The SKU identifier
        retailer (str): The name of the retailer
        
    Returns:
        pandas.DataFrame: A DataFrame containing the processed SKU data
        
    Raises:
        FileNotFoundError: If the CSV file for the given SKU and retailer doesn't exist
        Exception: If there are issues reading or processing the data
    """
    # Get the global path (assuming it's set in the environment)
    E_path = os.environ.get('E_PATH', r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved")
    
    val_path = os.path.join(E_path, 'SKU DATA', f'{retailer}')
    store_df = pd.read_csv(val_path + f"\\{sku}.csv")
    print("Successful read")
    
    store_df.rename(columns={
        'Store_Code': 'POC SAPID',
        'Store Code applied by the retailer': 'Store_Code',
        'Sales_Value': 'Sum(Value)',
        'Sales_Units': 'Sales_Units',
        'WEEK': 'Date'
    }, inplace=True)
    
    store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
    store_df['Year'] = store_df['Date'].dt.year
    store_df['Month'] = store_df['Date'].dt.month
    store_df['Periods'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    
    return store_df
```

## Next Steps
1. Create the `Code/data_processing.py` file with the content above
2. Update `Code/Accelerate.py` to import and use the modularized function
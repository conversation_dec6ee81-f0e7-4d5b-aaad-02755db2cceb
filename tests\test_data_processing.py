import unittest
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add the Code directory to the path so we can import from data_processing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_processing import mod1, read_data


class TestReadDataFunction(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
    
    def test_normal_case(self):
        """Test with standard input data."""
        # Create test Base_data DataFrame
        base_data = {
            'POC_SAPID': [1001, 1002, 1003],
            'col1': [10, 20, 30],
            'col2': [100, 200, 300]
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame
        meta_data = {
            'Store number': [1001, 1002],
            'START DATE': ['01/01/2023', '01/02/2023'],
            'END DATE': ['01/31/2023', '02/28/2023'],
            'TestvControl': ['Test', 'Control']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)  # DATA
        self.assertIsInstance(result[1], pd.DataFrame)  # META
        
        # Check that POC_SAPID was renamed to POC_ID and converted to string
        self.assertIn('POC_ID', result[0].columns)
        self.assertTrue(all(isinstance(x, str) for x in result[0]['POC_ID']))
        
        # Check that META columns were renamed
        self.assertIn('POC_ID', result[1].columns)
        self.assertIn('Start_Date', result[1].columns)
        self.assertIn('End_Date', result[1].columns)
        self.assertIn('Test_Control', result[1].columns)
        
        # Check that DATA and META were merged
        self.assertIn('Start_Date', result[0].columns)
        self.assertIn('End_Date', result[0].columns)
        self.assertIn('Test_Control', result[0].columns)
        
    def test_column_name_transformation(self):
        """Test conversion of column names with spaces to underscores."""
        # Create test Base_data DataFrame with spaces in column names
        base_data = {
            'POC_SAPID': [1001, 1002],
            'col with space': [10, 20],
            'another col': [10, 200]
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame
        meta_data = {
            'Store number': [1001],
            'START DATE': ['01/01/2023'],
            'END DATE': ['01/31/2023'],
            'TestvControl': ['Test']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        # Column names with spaces should be converted to underscores
        self.assertNotIn('col with space', result[0].columns)
        self.assertNotIn('another col', result[0].columns)
        self.assertIn('col_with_space', result[0].columns)
        self.assertIn('another_col', result[0].columns)
        
    def test_poc_id_conversion(self):
        """Test that POC_ID is properly converted to string."""
        # Create test Base_data DataFrame with integer POC_SAPID
        base_data = {
            'POC_SAPID': [1001, 1002, 1003],
            'value': [10, 20, 30]
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame
        meta_data = {
            'Store number': [1001, 1002],
            'START DATE': ['01/01/2023', '01/02/2023'],
            'END DATE': ['01/31/2023', '02/28/2023'],
            'TestvControl': ['Test', 'Control']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        # POC_SAPID should be renamed to POC_ID and converted to string
        self.assertIn('POC_ID', result[0].columns)
        self.assertTrue(all(isinstance(x, str) for x in result[0]['POC_ID']))
        self.assertTrue(all(isinstance(x, str) for x in result[1]['POC_ID']))
        
    def test_meta_column_renaming(self):
        """Test that META columns are properly renamed."""
        # Create test Base_data DataFrame
        base_data = {
            'POC_SAPID': [1001, 1002],
            'value': [10, 20]
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame with original column names
        meta_data = {
            'Store number': [1001],
            'START DATE': ['01/01/2023'],
            'END DATE': ['01/31/2023'],
            'TestvControl': ['Test']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        # META columns should be renamed
        self.assertIn('POC_ID', result[1].columns)
        self.assertIn('Start_Date', result[1].columns)
        self.assertIn('End_Date', result[1].columns)
        self.assertIn('Test_Control', result[1].columns)
        
        # Original column names should not exist
        self.assertNotIn('Store number', result[1].columns)
        self.assertNotIn('START DATE', result[1].columns)
        self.assertNotIn('END DATE', result[1].columns)
        self.assertNotIn('TestvControl', result[1].columns)
        
    def test_data_meta_merge(self):
        """Test that DATA and META are properly merged."""
        # Create test Base_data DataFrame
        base_data = {
            'POC_SAPID': [1001, 1002, 1003],
            'value': [10, 20, 30]
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame
        meta_data = {
            'Store number': [1001, 1002],
            'START DATE': ['01/01/2023', '01/02/2023'],
            'END DATE': ['01/31/2023', '02/28/2023'],
            'TestvControl': ['Test', 'Control']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        # DATA should contain merged columns from META
        self.assertIn('Start_Date', result[0].columns)
        self.assertIn('End_Date', result[0].columns)
        self.assertIn('Test_Control', result[0].columns)
        
        # Check that merge was done correctly (right join on META)
        # Only POC_IDs from META should be in the result
        meta_poc_ids = set([str(int(x)) for x in meta['Store number']])
        result_poc_ids = set(result[0]['POC_ID'].tolist())
        self.assertTrue(result_poc_ids.issubset(meta_poc_ids))


class TestMod1Function(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
    
    def test_normal_case(self):
        """Test with standard input data."""
        # Create test DataFrame
        data = {
            'POC SAPID': ['1001', '1002', '1001'],
            'Sum(Value)': [100, 200, 150],
            'Date': ['2023-01-01', '2023-01-02', '2023-01-01']
        }
        store_df = pd.DataFrame(data)
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('POC_SAPID', result.columns)
        # Check that data is duplicated (concatenated with itself)
        self.assertEqual(len(result), 2)  # Should have 2 unique POC_SAPIDs
        
    def test_negative_values(self):
        """Test handling of negative 'Sum(Value)' entries."""
        # Create test DataFrame with negative values
        data = {
            'POC SAPID': ['1001', '1002'],
            'Sum(Value)': [-50, 200],
            'Date': ['2023-01-01', '2023-01-02']
        }
        store_df = pd.DataFrame(data)
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions - negative values should be converted to 0
        # Since data is duplicated, we need to check the actual processing
        # The function should ensure all Sum(Value) >= 0
        # We'll check this indirectly by ensuring no errors occur
        
    def test_column_name_transformation(self):
        """Test conversion of column names with spaces to underscores."""
        # Create test DataFrame with spaces in column names
        data = {
            'POC SAPID': ['1001', '1002'],
            'Sum(Value)': [100, 200],
            'Date': ['2023-01-01', '2023-01-02']
        }
        store_df = pd.DataFrame(data)
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions
        # After processing, column names should have underscores
        # The final result should have POC_SAPID (with underscore)
        self.assertIn('POC_SAPID', result.columns)
        
    def test_empty_dataframe(self):
        """Test behavior with empty input."""
        # Create empty DataFrame with required column structure
        store_df = pd.DataFrame(columns=['POC SAPID', 'Sum(Value)', 'Date'])
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        # Should have POC_SAPID column but no data rows
        self.assertIn('POC_SAPID', result.columns)
        
    def test_date_processing(self):
        """Test date conversion and processing."""
        # Create test DataFrame
        data = {
            'POC SAPID': ['1001'],
            'Sum(Value)': [100],
            'Date': ['2023-01-15']
        }
        store_df = pd.DataFrame(data)
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions
        # Check that result has date columns (from pivot operation)
        # The exact column names depend on the date values
        self.assertIsInstance(result, pd.DataFrame)
        
    def test_duplicate_dates_aggregation(self):
        """Test aggregation of values for duplicate dates."""
        # Create test DataFrame with duplicate POC_SAPID and date
        data = {
            'POC SAPID': ['1001', '1001'],
            'Sum(Value)': [100, 50],
            'Date': ['2023-01-01', '2023-01-01']  # Same date
        }
        store_df = pd.DataFrame(data)
        
        # Call the function
        result = mod1(store_df)
        
        # Assertions
        # The function groups by POC_SAPID, Month, and Date and sums Sum(Value)
        # So for the same POC_SAPID and date, values should be summed
        self.assertIsInstance(result, pd.DataFrame)


if __name__ == '__main__':
    unittest.main()
import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_cleaning import remove_nulls_by_threshold_in_range, replace_nulls_with_0

class TestRemoveNullsByThresholdInRange(unittest.TestCase):
    
    def test_remove_nulls_no_nulls(self):
        """Test with data that has no nulls - all rows should be retained"""
        data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, 20, 30, 40, 50],
            'C': [100, 200, 300, 400, 500],
            'D': [1000, 2000, 3000, 4000, 5000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 50, 1, 3)
        # With 50% threshold on 2 columns (B, C), we need at least 1 non-null value
        # All rows should be retained since there are no nulls
        self.assertEqual(len(result), 5)
    
    def test_remove_nulls_with_nulls_below_threshold(self):
        """Test with some nulls but below threshold - all rows should be retained"""
        data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, np.nan, 30, np.nan, 50],
            'C': [100, 200, np.nan, 400, np.nan],
            'D': [1000, 2000, 3000, 4000, 5000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 50, 1, 3)
        # With 50% threshold on 2 columns (B, C), we need at least 1 non-null value
        # All rows have at least 1 non-null value in columns B,C, so all should be retained
        self.assertEqual(len(result), 5)
    
    def test_remove_nulls_with_nulls_above_threshold(self):
        """Test with some nulls above threshold - some rows should be removed"""
        data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, np.nan, 30, np.nan, 50],
            'C': [100, np.nan, np.nan, 400, np.nan],
            'D': [1000, 2000, 3000, 4000, 5000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 50, 1, 3)
        # With 50% threshold on 2 columns (B, C), we need at least 1 non-null value
        # Row 0: B=10, C=100 (2 non-null values) - retained
        # Row 1: B=np.nan, C=np.nan (0 non-null values) - removed
        # Row 2: B=30, C=np.nan (1 non-null value) - retained
        # Row 3: B=np.nan, C=400 (1 non-null value) - retained
        # Row 4: B=50, C=np.nan (1 non-null value) - retained
        # So 4 rows should be retained
        self.assertEqual(len(result), 4)
        # Check that row 1 (index 1) was removed
        self.assertNotIn(1, result.index)
    
    def test_remove_nulls_with_all_nulls_in_range(self):
        """Test with rows having all nulls in the range - those rows should be removed"""
        data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, np.nan, np.nan, np.nan, 50],
            'C': [100, np.nan, np.nan, np.nan, np.nan],
            'D': [1000, 2000, 3000, 4000, 5000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 50, 1, 3)
        # With 50% threshold on 2 columns (B, C), we need at least 1 non-null value
        # Rows 1, 2, 3 (indices 1, 2, 3) have all NaN in columns B,C - should be removed
        # Rows 0 and 4 have at least 1 non-null value - should be retained
        self.assertEqual(len(result), 2)
        # Check that the correct rows are retained (indices 0 and 4)
        self.assertIn(0, result.index)
        self.assertIn(4, result.index)
    
    def test_remove_nulls_high_threshold(self):
        """Test with high threshold - more strict filtering"""
        data = pd.DataFrame({
            'A': [1, 2, 3, 4, 5],
            'B': [10, np.nan, 30, 40, 50],
            'C': [100, 200, np.nan, 400, 500],
            'D': [1000, 2000, 3000, 4000, 5000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 100, 1, 3)
        # With 100% threshold on 2 columns (B, C), we need both values to be non-null
        # Only rows 0, 3, 4 have non-null values in both B and C
        # Row 1 has B=NaN, C=200 - only 1 non-null, should be removed
        # Row 2 has B=30, C=NaN - only 1 non-null, should be removed
        self.assertEqual(len(result), 3)
        self.assertIn(0, result.index)
        self.assertIn(3, result.index)
        self.assertIn(4, result.index)
    
    def test_remove_nulls_zero_threshold(self):
        """Test with zero threshold - all rows should be retained"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10, np.nan, 30],
            'C': [100, np.nan, np.nan],
            'D': [1000, 2000, 3000]
        })
        
        result = remove_nulls_by_threshold_in_range(data, 0, 1, 3)
        # With 0% threshold, we need 0 non-null values, so all rows should be retained
        self.assertEqual(len(result), 3)

class TestReplaceNullsWithZero(unittest.TestCase):
    
    def test_replace_nulls_no_nulls(self):
        """Test with data that has no nulls - data should remain unchanged"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10, 20, 30],
            'C': [100, 200, 300],
            'D': [1000, 2000, 3000]
        })
        columns = ['A', 'B', 'C', 'D']
        min_index = 1
        end_index = 2
        
        original_data = data.copy()
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Data should be unchanged since there were no nulls
        pd.testing.assert_frame_equal(result, original_data)
    
    def test_replace_nulls_all_nulls_in_range(self):
        """Test with all nulls in the specified range - all should be replaced with 0"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [np.nan, np.nan, np.nan],
            'C': [np.nan, np.nan, np.nan],
            'D': [1000, 2000, 3000]
        })
        columns = ['A', 'B', 'C', 'D']
        min_index = 1
        end_index = 2
        
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Check that all nulls in columns B and C are replaced with 0
        expected = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [0.0, 0.0, 0.0],
            'C': [0.0, 0.0, 0.0],
            'D': [1000, 2000, 3000]
        })
        pd.testing.assert_frame_equal(result, expected)
    
    def test_replace_nulls_mixed_values(self):
        """Test with mixed null and non-null values - only nulls should be replaced"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10, np.nan, 30],
            'C': [np.nan, 200, np.nan],
            'D': [1000, 2000, 3000]
        })
        columns = ['A', 'B', 'C', 'D']
        min_index = 1
        end_index = 2
        
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Check that only nulls are replaced with 0
        expected = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10.0, 0.0, 30.0],
            'C': [0.0, 200.0, 0.0],
            'D': [1000, 2000, 3000]
        })
        pd.testing.assert_frame_equal(result, expected)
    
    def test_replace_nulls_single_column_range(self):
        """Test with min_index equal to end_index - only one column should be processed"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10, np.nan, 30],
            'C': [100, 200, 300],
            'D': [1000, 2000, 3000]
        })
        columns = ['A', 'B', 'C', 'D']
        min_index = 1
        end_index = 1  # Same as min_index
        
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Only column B should have nulls replaced
        expected = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10.0, 0.0, 30.0],
            'C': [100, 200, 300],
            'D': [1000, 2000, 3000]
        })
        pd.testing.assert_frame_equal(result, expected)
    
    def test_replace_nulls_empty_dataframe(self):
        """Test with empty DataFrame - should return empty DataFrame"""
        data = pd.DataFrame(columns=['A', 'B', 'C', 'D'])
        columns = ['A', 'B', 'C', 'D']
        min_index = 1
        end_index = 2
        
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Should return empty DataFrame with same structure
        pd.testing.assert_frame_equal(result, data)
    
    def test_replace_nulls_no_columns_in_range(self):
        """Test with invalid range - should not modify data"""
        data = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [10, 20, 30],
            'C': [100, 200, 300]
        })
        columns = ['A', 'B', 'C']
        min_index = 3  # Out of range
        end_index = 2 # Less than min_index
        
        # This should not modify the data since the range is invalid
        original_data = data.copy()
        result = replace_nulls_with_0(data, min_index, end_index, columns)
        
        # Data should remain unchanged
        pd.testing.assert_frame_equal(result, original_data)

if __name__ == '__main__':
    unittest.main()
# Test Implementation Plan for `lift_outlier_iqr` Function

## Overview
This document outlines the plan for implementing tests for the `lift_outlier_iqr` function in the `tests/test_analysis.py` file.

## Test Structure
Following the existing pattern in the project, the tests should be added to `tests/test_analysis.py` and should include a new test class `TestLiftOutlierIQR` with methods for each test case.

## Test Cases to Implement

### 1. Normal Case Test
- Create a DataFrame with mixed outlier and non-outlier data
- Verify that rows are correctly classified
- Check that 'Outlier' and 'Outlier_Reason' columns are added correctly

### 2. All Values Within Thresholds Test
- Create a DataFrame where all 'ABI-%Lift' values are between -300 and 300
- Verify that all rows are marked as "No" for Outlier
- Check that 'Outlier_Reason' is empty for all rows

### 3. All Values Beyond Thresholds Test
- Create a DataFrame where all 'ABI-%Lift' values are beyond the thresholds
- Verify that all rows are marked as "Yes" for Outlier
- Check that 'Outlier_Reason' is "Uplift beyond threshold" for all rows

### 4. Missing Columns Test
- Create a DataFrame missing one or more required columns
- Verify that the function handles this gracefully
- Check for appropriate error handling or warnings

### 5. All NaN Values Test
- Create a DataFrame where all rows have NaN values in key columns
- Verify that all rows are marked as "Yes" for Outlier
- Check that 'Outlier_Reason' correctly indicates the specific reasons

### 6. All Negative Values Test
- Create a DataFrame where all rows have negative values in key columns
- Verify that all rows are marked as "Yes" for Outlier
- Check that 'Outlier_Reason' correctly indicates the specific reasons

### 7. Control Count Edge Cases Test
- Create a DataFrame with various 'ABI-Control count' values (0, 1, 4, 5, 6, NaN)
- Verify correct classification based on the 5 threshold
- Check that 'Outlier_Reason' is appropriate for each case

### 8. Mixed Conditions Test
- Create a DataFrame with rows meeting different outlier criteria
- Verify that each row is correctly classified based on its specific conditions
- Check that 'Outlier_Reason' accurately reflects why each row was marked as an outlier

### 9. Empty DataFrame Test
- Create an empty DataFrame with the required column structure
- Verify that the function returns the same empty DataFrame with added columns
- Check that no exceptions are raised

### 10. Single Row DataFrame Test
- Create a DataFrame with a single row of data
- Verify that the row is correctly classified based on its values
- Check that the required columns are added and the function returns the modified DataFrame

## Implementation Approach
1. Add the new test class `TestLiftOutlierIQR` to `tests/test_analysis.py`
2. Implement each test method following the existing patterns in the file
3. Use `setUp` method if needed to create common test data
4. Use assertions to verify expected behavior
5. Follow the same import patterns as existing tests

## Dependencies
- pandas
- numpy
- unittest
- The `lift_outlier_iqr` function from `analysis` module

## Success Criteria
1. All test cases pass
2. Tests follow the existing project patterns
3. Edge cases are properly handled
4. Error conditions are appropriately tested
5. Code coverage is sufficient for the function's complexity
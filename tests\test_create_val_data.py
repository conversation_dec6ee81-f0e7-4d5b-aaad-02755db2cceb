import unittest
import pandas as pd
import os
import sys
import tempfile
import numpy as np
from unittest.mock import patch, MagicMock

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_processing import create_val_data

class TestCreateValData(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create sample SKU data CSV for testing
        self.sample_data = pd.DataFrame({
            'Store_Code': ['S001', 'S002', 'S003'],
            'Store Code applied by the retailer': ['Store1', 'Store2', 'Store3'],
            'Sales_Value': [100.50, 200.75, 150.25],
            'Sales_Units': [10, 20, 15],
            'WEEK': ['2023-01-01', '2023-01-08', '2023-01-15']
        })
        
        # Create SKU DATA directory structure
        os.makedirs(os.path.join(self.test_dir, 'SKU DATA', 'Asda'), exist_ok=True)
        os.makedirs(os.path.join(self.test_dir, 'SKU DATA', 'Tesco'), exist_ok=True)
        
        # Save sample CSV files
        self.sample_csv_path = os.path.join(self.test_dir, 'SKU DATA', 'Asda', '2000015054018.csv')
        self.sample_data.to_csv(self.sample_csv_path, index=False)
        
        # Store original path and update for testing
        self.original_path = os.environ.get('E_PATH', '')
        os.environ['E_PATH'] = self.test_dir
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.test_dir)
        # Restore original path
        if self.original_path:
            os.environ['E_PATH'] = self.original_path
        else:
            os.environ.pop('E_PATH', None)
    
    def test_normal_case(self):
        """Test normal case with valid sku and retailer that have matching data"""
        result = create_val_data('2000015054018', 'Asda')
        
        # Check that result is a DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        
        # Check that the DataFrame has the expected number of rows
        self.assertEqual(len(result), 3)
        
        # Check that columns were renamed correctly
        expected_columns = ['Store_Code', 'POC SAPID', 'Sum(Value)', 'Sales_Units', 'Date', 'Year', 'Month', 'Periods']
        for col in expected_columns:
            self.assertIn(col, result.columns)
        
        # Check that values were processed correctly
        self.assertEqual(result['POC SAPID'].iloc[0], 'S001')
        self.assertEqual(result['Store_Code'].iloc[0], 'Store1')
        self.assertEqual(result['Sum(Value)'].iloc[0], 100.50)
        self.assertEqual(result['Sales_Units'].iloc[0], 10)
        
        # Check date processing
        self.assertEqual(result['Date'].iloc[0].strftime('%Y-%m-%d'), '2023-01-01')
        self.assertEqual(result['Year'].iloc[0], 2023)
        self.assertEqual(result['Month'].iloc[0], 1)
        
        # Check Periods column
        self.assertEqual(result['Periods'].iloc[0].strftime('%Y-%m-%d'), '2023-01-01')
    
    def test_non_existent_file(self):
        """Test with a sku/retailer combination that doesn't have a corresponding CSV file"""
        with self.assertRaises(FileNotFoundError):
            create_val_data('nonexistent_sku', 'Asda')
    
    def test_invalid_retailer(self):
        """Test with a retailer name that doesn't have a corresponding directory"""
        with self.assertRaises(FileNotFoundError):
            create_val_data('2000015054018', 'InvalidRetailer')
    
    def test_empty_csv_file(self):
        """Test with an empty CSV file"""
        # Create empty CSV file
        empty_csv_path = os.path.join(self.test_dir, 'SKU DATA', 'Asda', 'empty_sku.csv')
        pd.DataFrame(columns=['Store_Code', 'Store Code applied by the retailer', 'Sales_Value', 'Sales_Units', 'WEEK']).to_csv(empty_csv_path, index=False)
        
        result = create_val_data('empty_sku', 'Asda')
        
        # Should return an empty DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        expected_columns = ['Store_Code', 'POC SAPID', 'Sum(Value)', 'Sales_Units', 'Date', 'Year', 'Month', 'Periods']
        for col in expected_columns:
            self.assertIn(col, result.columns)
        self.assertEqual(len(result), 0)
    
    def test_missing_columns_in_csv_file(self):
        """Test with a CSV file that is missing required columns"""
        # Create CSV with missing columns
        bad_data = pd.DataFrame({
            'Store_Code': ['S001', 'S002'],
            'Sales_Value': [100.50, 200.75]
            # Missing other required columns like 'Store Code applied by the retailer', 'Sales_Units', 'WEEK'
        })
        bad_csv_path = os.path.join(self.test_dir, 'SKU DATA', 'Asda', 'bad_sku.csv')
        bad_data.to_csv(bad_csv_path, index=False)
        
        result = create_val_data('bad_sku', 'Asda')
        
        # Should still return a DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        # Check that existing columns were processed
        self.assertIn('POC SAPID', result.columns)  # Renamed from Store_Code
        self.assertIn('Sum(Value)', result.columns)  # Renamed from Sales_Value
        # Check that missing columns are handled gracefully
        # Note: 'Store Code applied by the retailer' is renamed to 'Store_Code'
        # 'Sales_Units' and 'WEEK' (renamed to 'Date') are missing from input, so they'll be created as empty
        # The function should create all expected columns even if some are missing from input
        expected_columns = ['POC SAPID', 'Sum(Value)', 'Store_Code', 'Sales_Units', 'Date', 'Year', 'Month', 'Periods']
        for col in expected_columns:
            self.assertIn(col, result.columns)
    
    def test_invalid_date_formats(self):
        """Test with a CSV file that has invalid date formats in the WEEK column"""
        # Create CSV with invalid dates
        bad_date_data = pd.DataFrame({
            'Store_Code': ['S001', 'S002'],
            'Store Code applied by the retailer': ['Store1', 'Store2'],
            'Sales_Value': [100.50, 200.75],
            'Sales_Units': [10, 20],
            'WEEK': ['invalid_date', '2023-01-08']  # One invalid date
        })
        bad_date_csv_path = os.path.join(self.test_dir, 'SKU DATA', 'Asda', 'bad_date_sku.csv')
        bad_date_data.to_csv(bad_date_csv_path, index=False)
        
        result = create_val_data('bad_date_sku', 'Asda')
        
        # Should handle invalid dates gracefully (coerce to NaT)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        # First row should have NaT for Date
        self.assertTrue(pd.isna(result['Date'].iloc[0]))
        # Second row should have proper date
        self.assertEqual(result['Date'].iloc[1].strftime('%Y-%m-%d'), '2023-01-08')
    
    def test_special_characters_in_sku(self):
        """Test with SKU values that contain special characters"""
        # Create CSV with special characters in filename
        special_sku_data = pd.DataFrame({
            'Store_Code': ['S001'],
            'Store Code applied by the retailer': ['Store1'],
            'Sales_Value': [100.50],
            'Sales_Units': [10],
            'WEEK': ['2023-01-01']
        })
        special_sku_csv_path = os.path.join(self.test_dir, 'SKU DATA', 'Asda', 'special-sku_123.csv')
        special_sku_data.to_csv(special_sku_csv_path, index=False)
        
        result = create_val_data('special-sku_123', 'Asda')
        
        # Should handle special characters in SKU correctly
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 1)

if __name__ == '__main__':
    unittest.main()
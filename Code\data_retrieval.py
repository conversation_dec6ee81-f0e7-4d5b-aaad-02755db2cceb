import pandas as pd
import os

def get_sku_list(retailer, brand):
    """
    Retrieve a list of SKU items for a given retailer and brand.
    
    Args:
        retailer (str): The name of the retailer
        brand (str): The name of the brand
        
    Returns:
        pandas.DataFrame: A DataFrame containing the matching SKU items
    """
    # Get the global path (assuming it's set in the environment)
    E_path = os.environ.get('E_PATH', r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved")
    sku_list = pd.read_csv(E_path + "\\SKU List.csv").query("Retailer == @retailer and BRAND == @brand")
    return sku_list

def create_pilot_df(activity_id, retailer):
    """
    Create a pilot DataFrame with test and control stores for a given activity.
    
    Args:
        activity_id (str): The activity/campaign ID
        retailer (str): The name of the retailer
        
    Returns:
        pandas.DataFrame: A DataFrame containing test and control stores with their assignments
    """
    # Use hardcoded path like original
    E_path = r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved"
    
    # Read test store list and filter by campaign ID
    test_list = pd.read_excel(E_path+"\\Test Store List.xlsx").query("Campaign_ID == @activity_id")
    test_list['TestvControl']="Test"
    store_list = pd.read_excel(E_path+"\\Store Codes"+f"\\{retailer}.xlsx")
    control_list = store_list[~store_list['Store code applied by the retailer'].isin(test_list['Store_Id'])]
    control_list['TestvControl'] = "Control"
    control_list = control_list.rename(columns={'Store code applied by the retailer':'Store_Id'})
    control_list.insert(0,'Campaign_ID',activity_id)
    print(control_list.head())
    pilot_df = pd.concat([test_list, control_list], ignore_index=True)
    print(pilot_df['TestvControl'].unique())
    pilot_df.to_excel(E_path+"\\pilot_df.xlsx")
    return pilot_df
# Modularization of `get_activation_week_index` Function

## Overview

This document summarizes the modularization of the `get_activation_week_index` function from `Code/Accelerate.py` to `Code/utils.py`.

## Changes Made

### 1. Function Migration
- **Moved**: `get_activation_week_index` function from `Code/Accelerate.py` to `Code/utils.py`
- **Added**: `get_end_week_index` function to `Code/utils.py` (similar function that was also in `Accelerate.py`)
- **Reason**: Both functions are utility functions for date manipulation and index finding, making `utils.py` the appropriate module

### 2. Documentation
- Added comprehensive docstrings to both functions following the existing patterns in `utils.py`
- Documented parameters, return values, and possible exceptions

### 3. Import Updates
- **Updated**: Import statement in `Code/Accelerate.py` to import the modularized functions:
  ```python
  from utils import find_date, get_data_indices_n_years, get_activation_week_index, get_end_week_index
  ```
- **Removed**: Original function definitions from `Code/Accelerate.py`

### 4. Testing
- **Added**: Comprehensive tests for both functions in `tests/test_utils.py`:
  - `TestGetActivationWeekIndex` class with multiple test methods
  - `TestGetEndWeekIndex` class with multiple test methods
- **Coverage**: Tests cover normal operation, error conditions, and edge cases:
  - Normal case - date exists in columns
  - Date not found in columns (ValueError)
  - Empty columns list (ValueError)
  - Multiple occurrences of the same date (first occurrence)
  - Boundary cases (date at beginning, end, and single element list)

## Test Results

All tests pass successfully:
- 20 tests in `tests/test_utils.py` (including 6 new tests for the modularized functions)
- 104 total tests in the project suite
- No regressions introduced

## Benefits Achieved

1. **Improved Code Organization**: Utility functions are now grouped together in `utils.py`
2. **Better Maintainability**: Changes to these functions only require updates in one location
3. **Enhanced Testability**: Functions can be tested in isolation with comprehensive test coverage
4. **Reduced Complexity**: `Accelerate.py` is now smaller and more focused on its main orchestration role
5. **Code Reusability**: The functions can be easily imported and used in other modules if needed

## Functions Details

### get_activation_week_index
```python
def get_activation_week_index(date, test_data_columns):
    """
    Find the index of a date in a list of column names.
    
    This function formats a date to 'YYYY-MM-DD' and finds its index
    in the provided list of column names.
    
    Args:
        date (datetime): The date to find
        test_data_columns (list): A list of column names (strings)
        
    Returns:
        int: The index of the formatted date in the column list
        
    Raises:
        ValueError: If the formatted date is not found in the column list
    """
```

### get_end_week_index
```python
def get_end_week_index(date, test_data_columns):
    """
    Find the index of a date in a list of column names.
    
    This function formats a date to 'YYYY-MM-DD' and finds its index
    in the provided list of column names.
    
    Args:
        date (datetime): The date to find
        test_data_columns (list): A list of column names (strings)
        
    Returns:
        int: The index of the formatted date in the column list
        
    Raises:
        ValueError: If the formatted date is not found in the column list
    """
```

## Usage

The functions are used in the `accelerate` function in `Code/Accelerate.py`:
```python
# Before modularization (implicit):
activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
activation_end = get_end_week_index(global_test.End_Date.iloc[0], list(global_test))

# After modularization (explicit):
activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
activation_end = get_end_week_index(global_test.End_Date.iloc[0], list(global_test))
```

The usage remains exactly the same, but the functions are now imported from `utils.py` rather than being defined locally in `Accelerate.py`.

## Next Steps

This modularization serves as a template for moving other functions from `Accelerate.py` to appropriate modules:
1. Identify similar utility functions in `Accelerate.py`
2. Follow the same process for migration
3. Create comprehensive tests
4. Update imports
5. Validate with full test suite
# Modularization Plan for `lift_outlier_iqr` Function

## Overview
This document outlines the plan for modularizing the `lift_outlier_iqr` function from `Code/Accelerate.py` to `Code/analysis.py`. The function is responsible for identifying outliers in the results data based on various criteria.

## Current State Analysis
The `lift_outlier_iqr` function is currently defined in `Code/Accelerate.py` at lines 147-184. It is used in the same file at line 436 where it's called as `APT_Outlier = lift_outlier_iqr(APT_RESULTS)`.

## Function Dependencies
The function depends on the following:
1. pandas DataFrame operations
2. Specific column names in the input DataFrame:
   - 'ABI-%Lift'
   - 'ABI-Test analysis period'
   - 'ABI-Control analysis period'
   - 'ABI-Test baseline period'
   - 'ABI-Control baseline period'
   - 'ABI-Control count'

## Modularization Steps

### 1. Add Function to analysis.py
- Copy the `lift_outlier_iqr` function from `Code/Accelerate.py` to `Code/analysis.py`
- Add appropriate documentation following the style used in other functions in the module
- Place the function after the existing functions but before the module's end

### 2. Update Imports in Accelerate.py
- Modify the import statement on line 37 in `Code/Accelerate.py` to include `lift_outlier_iqr`
- Current import: `from analysis import testvscontrolfcn, get_clustered_data, get_dist_mat_grp, get_optimal_n_cluster, get_uplift, significance_level`
- Updated import: `from analysis import testvscontrolfcn, get_clustered_data, get_dist_mat_grp, get_optimal_n_cluster, get_uplift, significance_level, lift_outlier_iqr`

### 3. Remove Function from Accelerate.py
- Remove the function definition from `Code/Accelerate.py` (lines 147-184)
- Ensure no other references to the function within the file need to be modified

## Integration Verification
After modularization, verify that:
1. The function is correctly imported in `Accelerate.py`
2. The function call at line 436 still works correctly
3. The overall workflow in the `accelerate` function continues to work as expected
4. No import errors occur when running the script

## Testing Approach
1. Run existing tests to ensure no regressions
2. Create new tests specifically for the `lift_outlier_iqr` function in `tests/test_analysis.py`
3. Verify the function works correctly with the test plan outlined in `test_plan_lift_outlier_iqr.md`

## Rollback Plan
If issues arise during modularization:
1. Revert changes to `Code/analysis.py`
2. Restore the function to `Code/Accelerate.py`
3. Revert import changes in `Code/Accelerate.py`
4. Identify and fix the root cause of the issue
5. Retry the modularization

## Success Criteria
1. The function works identically in the modularized form as it did in the original location
2. All existing functionality remains intact
3. New tests pass successfully
4. No import errors or runtime errors occur
5. Code follows the established patterns in the project
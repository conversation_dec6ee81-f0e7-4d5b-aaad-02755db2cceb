import unittest
import pandas as pd
import os
import sys
import tempfile
from unittest.mock import patch, MagicMock

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from orchestration import initialize_accelerate
from data_retrieval import create_pilot_df

class TestInitializeAccelerate(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create sample data for testing
        self.sample_pilot_data = pd.DataFrame({
            'Campaign_ID': ['ACT123'],
            'Store_Id': [1001],
            'TestvControl': ['Test']
        })
        
        # Create directories
        os.makedirs(os.path.join(self.test_dir, 'META'), exist_ok=True)
        
        # Store original path and update for testing
        self.original_path = os.environ.get('E_PATH', '')
        os.environ['E_PATH'] = self.test_dir
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.test_dir)
        # Restore original path
        if self.original_path:
            os.environ['E_PATH'] = self.original_path
        else:
            os.environ.pop('E_PATH', None)
    
    @patch('orchestration.create_pilot_df')
    @patch('orchestration.os.makedirs')
    @patch('orchestration.os.path.isdir')
    def test_initialize_accelerate_normal_case(self, mock_isdir, mock_makedirs, mock_create_pilot_df):
        """Test normal case with valid parameters"""
        # Setup mocks
        mock_isdir.return_value = False  # Directory doesn't exist, so makedirs will be called
        mock_create_pilot_df.return_value = self.sample_pilot_data.copy()
        
        # Call the function
        from datetime import datetime
        result = initialize_accelerate(
            activity_id='ACT123',
            retailer='Asda',
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            campaign='Test Campaign'
        )
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        mock_isdir.assert_called_once_with(os.path.join(self.test_dir, 'META'))
        mock_makedirs.assert_called_once_with(os.path.join(self.test_dir, 'META'), exist_ok=True)
        mock_create_pilot_df.assert_called_once_with('ACT123', 'Asda')
        
        # Check that the result has the expected modifications
        self.assertIn('START DATE', result.columns)
        self.assertIn('END DATE', result.columns)
        self.assertIn('POC_ID', result.columns)
        self.assertEqual(result['START DATE'].iloc[0], datetime(2023, 1, 1))
        self.assertEqual(result['END DATE'].iloc[0], datetime(2023, 12, 31))
    
    @patch('orchestration.create_pilot_df')
    @patch('orchestration.os.makedirs')
    @patch('orchestration.os.path.isdir')
    def test_initialize_accelerate_directory_exists(self, mock_isdir, mock_makedirs, mock_create_pilot_df):
        """Test case where META directory already exists"""
        # Setup mocks
        mock_isdir.return_value = True  # Directory already exists
        mock_create_pilot_df.return_value = self.sample_pilot_data.copy()
        
        # Call the function
        from datetime import datetime
        result = initialize_accelerate(
            activity_id='ACT123',
            retailer='Asda',
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            campaign='Test Campaign'
        )
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        mock_isdir.assert_called_once_with(os.path.join(self.test_dir, 'META'))
        mock_makedirs.assert_not_called()  # Should not be called since directory exists
        mock_create_pilot_df.assert_called_once_with('ACT123', 'Asda')
    
    @patch('orchestration.create_pilot_df')
    def test_initialize_accelerate_metadata_processing(self, mock_create_pilot_df):
        """Test that metadata is correctly processed and saved"""
        # Setup mock
        mock_create_pilot_df.return_value = self.sample_pilot_data.copy()
        
        # Call the function
        from datetime import datetime
        result = initialize_accelerate(
            activity_id='ACT123',
            retailer='Asda',
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            campaign='Test Campaign'
        )
        
        # Check that the result has the expected modifications
        self.assertEqual(result['START DATE'].iloc[0], datetime(2023, 1, 1))
        self.assertEqual(result['END DATE'].iloc[0], datetime(2023, 12, 31))
        self.assertIn('POC_ID', result.columns)
        # Check that column was renamed
        self.assertNotIn('Store_Id', result.columns)
        self.assertIn('POC_ID', result.columns)
    
    @patch('orchestration.print')  # Mock print to avoid console output during tests
    @patch('orchestration.create_pilot_df')
    def test_initialize_accelerate_print_output(self, mock_create_pilot_df, mock_print):
        """Test that the function prints the expected output"""
        # Setup mock
        mock_create_pilot_df.return_value = self.sample_pilot_data.copy()
        
        # Call the function
        from datetime import datetime
        result = initialize_accelerate(
            activity_id='ACT123',
            retailer='Asda',
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            campaign='Test Campaign'
        )
        
        # Check that print was called with the correct arguments
        mock_print.assert_called_once_with('Test Campaign', 'ACT123', 'Asda')
    
    def test_initialize_accelerate_invalid_parameters(self):
        """Test case with invalid parameters"""
        from datetime import datetime
        # Test with None values
        with self.assertRaises(Exception):
            initialize_accelerate(
                activity_id=None,
                retailer=None,
                start_date=None,
                end_date=None,
                campaign=None
            )

    @patch('orchestration.pd.concat')
    @patch('orchestration.pd.DataFrame.to_excel')
    def test_aggregate_results_output(self, mock_to_excel, mock_concat):
        """Test results aggregation and output functionality"""
        from orchestration import aggregate_results_output
        
        # Setup test data
        mock_final_output = MagicMock()
        mock_apt_results = MagicMock()
        mock_test_control = MagicMock()
        activity_id = "ACT123"
        sku = "SKU456"
        e_path = "/test/path"
        
        # Call the function
        result = aggregate_results_output(
            mock_final_output,
            mock_apt_results,
            mock_test_control,
            activity_id,
            sku,
            e_path
        )
        
        # Verify function calls
        mock_concat.assert_called_once_with([mock_final_output, mock_apt_results], ignore_index=True)
        mock_test_control.to_excel.assert_called_once_with(
            os.path.join(e_path, "TestvCtrl", f"{activity_id}.xlsx"),
            sheet_name="Control_Mapping",
            index=False
        )
        self.assertEqual(result, mock_concat.return_value)

if __name__ == '__main__':
    unittest.main()
import pandas as pd
import numpy as np
import sys
import os

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Code'))

# Import the original function from the new module
from analysis import compare_test_control

# Test case from test_normal_case
control_data = pd.DataFrame({
    'POC_ID': [1, 2, 3],
    'Start_Date': ['2023-01-01', '2023-01-01', '2023-01-01'],
    'End_Date': ['2023-12-31', '2023-12-31', '2023-12-31'],
    'NET_AVG_Y1': [100, 200, 300],
    '2023-01-01': [10, 20, 30],
    '2023-01-08': [15, 25, 35],
    '2023-01-15': [12, 22, 32]
})

test_data = pd.DataFrame({
    'POC_ID': [4, 5],
    'Start_Date': ['2023-01-01', '2023-01-01'],
    'End_Date': ['2023-12-31', '2023-12-31'],
    'NET_AVG_Y1': [150, 250],
    '2023-01-01': [18, 28],
    '2023-01-08': [23, 33],
    '2023-01-15': [20, 30]
})

result = compare_test_control(control_data, test_data)
print("Normal case result:")
print(result)
print()

# Check values
control_means = result.loc['Control']
test_means = result.loc['Test']

print("Control means:")
print(f"2023-01-01: {control_means['2023-01-01']}")
print(f"2023-01-08: {control_means['2023-01-08']}")
print(f"2023-01-15: {control_means['2023-01-15']}")
print()

print("Test means:")
print(f"2023-01-01: {test_means['2023-01-01']}")
print(f"2023-01-08: {test_means['2023-01-08']}")
print(f"2023-01-15: {test_means['2023-01-15']}")
print()

# Test case from test_missing_values
control_data = pd.DataFrame({
    'POC_ID': [1, 2, 3],
    'Start_Date': ['2023-01-01', '2023-01-01', '2023-01-01'],
    'End_Date': ['2023-12-31', '2023-12-31', '2023-12-31'],
    'NET_AVG_Y1': [100, 200, 300],
    '2023-01-01': [10, np.nan, 30],
    '2023-01-08': [15, 25, np.nan],
    '2023-01-15': [np.nan, np.nan, np.nan]
})

test_data = pd.DataFrame({
    'POC_ID': [4, 5],
    'Start_Date': ['2023-01-01', '2023-01-01'],
    'End_Date': ['2023-12-31', '2023-12-31'],
    'NET_AVG_Y1': [150, 250],
    '2023-01-01': [18, np.nan],
    '2023-01-08': [np.nan, 33],  # Changed to 33 to match the test
    '2023-01-15': [20, 30]
})

result = compare_test_control(control_data, test_data)
print("Missing values case result:")
print(result)
print()

# Check values
control_means = result.loc['Control']
test_means = result.loc['Test']

print("Control means:")
print(f"2023-01-01: {control_means['2023-01-01']}")
print(f"2023-01-08: {control_means['2023-01-08']}")
print(f"2023-01-15: {control_means['2023-01-15']}")
print()

print("Test means:")
print(f"2023-01-01: {test_means['2023-01-01']}")
print(f"2023-01-08: {test_means['2023-01-08']}")
print(f"2023-01-15: {test_means['2023-01-15']}")
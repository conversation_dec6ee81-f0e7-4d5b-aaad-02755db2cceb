from datetime import datetime

def find_date(min_date, max_date):
    """
    Convert datetime objects to formatted date strings.
    
    Args:
        min_date (datetime): The minimum date
        max_date (datetime): The maximum date
        
    Returns:
        tuple: A tuple containing two strings formatted as 'YYYY-MM-DD'
    """
    min_col = min_date.strftime('%Y-%m-%d')
    max_col = max_date.strftime('%Y-%m-%d')
    return (min_col, max_col)

def get_activation_week_index(date, test_data_columns):
    """
    Find the index of a date in a list of column names.
    
    This function formats a date to 'YYYY-MM-DD' and finds its index
    in the provided list of column names.
    
    Args:
        date (datetime): The date to find
        test_data_columns (list): A list of column names (strings)
        
    Returns:
        int: The index of the formatted date in the column list
        
    Raises:
        ValueError: If the formatted date is not found in the column list
    """
    # Format date to 'YYYY-MM-DD'
    col = date.strftime('%Y-%m-%d')
    
    # Get index of the formatted date
    act_ind = test_data_columns.index(col)
    
    return act_ind

def get_end_week_index(date, test_data_columns):
    """
    Find the index of a date in a list of column names.
    
    This function formats a date to 'YYYY-MM-DD' and finds its index
    in the provided list of column names.
    
    Args:
        date (datetime): The date to find
        test_data_columns (list): A list of column names (strings)
        
    Returns:
        int: The index of the formatted date in the column list
        
    Raises:
        TypeError: If date is not a datetime object or test_data_columns is not a list
        ValueError: If the formatted date is not found in the column list
        ValueError: If test_data_columns is empty
    """
    # Input validation
    if not hasattr(date, 'strftime'):
        raise TypeError("date must be a datetime object")
    
    if not isinstance(test_data_columns, list):
        raise TypeError("test_data_columns must be a list")
    
    if len(test_data_columns) == 0:
        raise ValueError("test_data_columns cannot be empty")
    
    # Format date to 'YYYY-MM-DD'
    try:
        col = date.strftime('%Y-%m-%d')
    except Exception as e:
        raise TypeError(f"date must be a datetime object. Error: {str(e)}")
    
    # Get index of the formatted date
    try:
        end_ind = test_data_columns.index(col)
    except ValueError:
        raise ValueError(f"Date {col} not found in test_data_columns")
    
    return end_ind

def get_data_indices_n_years(columns):
    """
    Identify date columns in a list, find the earliest and latest dates,
    and return their indices along with corresponding years.
    
    Args:
        columns (list): A list of column names (strings)
        
    Returns:
        tuple: A tuple containing (index_min, index_max, min_year, max_year)
        
    Raises:
        ValueError: If no valid date columns are found in the format YYYY-MM-DD
    """
    date_columns = []
    date_indices = {}
    for i, col in enumerate(columns):
        try:
            dt = datetime.strptime(col, '%Y-%m-%d')
            date_columns.append(dt)
            date_indices[dt] = i
        except:
            continue  # Skip non-date columns like POC_ID, Start_Date, etc.

    if not date_columns:
        raise ValueError("No valid date columns found in the format YYYY-MM-DD.")
    min_date = min(date_columns)
    max_date = max(date_columns)
    index_min = date_indices[min_date]
    index_max = date_indices[max_date]
    return index_min, index_max, min_date.year, max_date.year
# Test Plan for `filter_control_pocs` Function

## Function Overview
The `filter_control_pocs` function filters control points of sale (POCs) based on their similarity to a test POC using RMSE (Root Mean Square Error) calculation. It ranks control POCs by RMSE and returns only those within a specified limit.

### Function Signature
```python
def filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit):
```

### Parameters
- `global_ctrl`: DataFrame containing control POC data
- `global_test`: DataFrame containing test POC data
- `min_index`: Integer representing the starting column index for baseline period
- `activation_on`: Integer representing the column index for activation date
- `limit`: Integer representing the maximum number of control POCs to return

### Dependencies
- `sklearn.metrics.mean_squared_error`
- `math.sqrt`
- pandas DataFrame operations

## Test Cases

### 1. Normal Case
- Create sample DataFrames for global_ctrl and global_test with valid data
- Set appropriate min_index, activation_on, and limit values
- Verify that the function returns the expected filtered DataFrame

### 2. Empty DataFrame
- Test with empty global_ctrl DataFrame
- Verify that the function handles this gracefully

### 3. Missing Columns
- Test with DataFrames that are missing required columns
- Verify that the function handles this gracefully

### 4. Mismatched Indices
- Test with DataFrames that have mismatched column indices
- Verify that the function handles this gracefully

### 5. Limit Greater Than Control POCs
- Test with a limit value greater than the number of control POCs
- Verify that all control POCs are returned

### 6. Limit Less Than Control POCs
- Test with a limit value less than the number of control POCs
- Verify that only the specified number of control POCs are returned

### 7. DataFrames with NaN Values
- Test with DataFrames containing NaN values
- Verify that the function handles this appropriately

### 8. Single Control POC
- Test with only one control POC
- Verify that it's returned correctly

### 9. Identical Test and Control Data
- Test with identical test and control data
- Verify that RMSE is calculated correctly (should be 0)

## Test File Structure
Based on the existing test files, the test file should be structured as follows:

```python
import unittest
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error
import math
import sys
import os

# Add the Code directory to the path so we can import from Accelerate.py
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Code'))

from Accelerate import filter_control_pocs

class TestFilterControlPocsFunction(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
    
    def test_normal_case(self):
        """Test with standard input data."""
        # Implementation
    
    def test_empty_dataframe(self):
        """Test behavior with empty input."""
        # Implementation
    
    def test_missing_columns(self):
        """Test behavior with missing columns."""
        # Implementation
    
    def test_mismatched_indices(self):
        """Test behavior with mismatched indices."""
        # Implementation
    
    def test_limit_greater_than_controls(self):
        """Test with limit greater than number of control POCs."""
        # Implementation
    
    def test_limit_less_than_controls(self):
        """Test with limit less than number of control POCs."""
        # Implementation
    
    def test_nan_values(self):
        """Test with NaN values in data."""
        # Implementation
    
    def test_single_control_poc(self):
        """Test with single control POC."""
        # Implementation
    
    def test_identical_data(self):
        """Test with identical test and control data."""
        # Implementation

if __name__ == '__main__':
    unittest.main()
```

## Implementation Details

### Test Data Creation
- Create sample DataFrames with realistic column structures
- Include POC_ID, date columns, and value columns
- Ensure proper indexing for min_index and activation_on parameters

### Expected Results
- For normal case, verify that RMSE values are calculated correctly
- For edge cases, verify that appropriate exceptions are raised or handled gracefully
- For limit cases, verify that the correct number of control POCs are returned

### Assertions
- Check return type is DataFrame
- Check that returned DataFrame has correct columns
- Check that returned DataFrame has correct number of rows based on limit
- Check that RMSE values are calculated correctly
- Check that RMSE_Rank column is added correctly
- Check that "RMSE Value" column is added correctly
# Test Plan for `create_pilot_df` Function

## Overview
This document outlines the test plan for the `create_pilot_df` function before and after modularization. The function is responsible for creating a pilot dataframe that combines test and control stores for a given activity ID and retailer.

## Function Analysis
The `create_pilot_df` function performs the following operations:
1. Takes `activity_id` and `retailer` as parameters
2. Reads from "Test Store List.xlsx" and filters by Campaign_ID
3. Reads from "Store Codes/{retailer}.xlsx" to get control stores
4. Creates a combined dataframe with test and control stores
5. Saves the result to "pilot_df.xlsx"
6. Returns the combined dataframe

## Test Cases

### 1. Normal Case
**Description**: Test the function with valid activity_id and retailer that have matching data.
**Expected Behavior**: 
- Should return a DataFrame with both test and control stores
- Test stores should have "Test" in the TestvControl column
- Control stores should have "Control" in the TestvControl column
- The pilot_df.xlsx file should be created with the combined data

### 2. No Matching Activity ID
**Description**: Test with an activity_id that doesn't exist in the Test Store List.
**Expected Behavior**: 
- Should return a DataFrame with only control stores (no test stores)
- Control stores should still have "Control" in the TestvControl column
- The pilot_df.xlsx file should be created with only control stores

### 3. Empty Test Store File
**Description**: Test with an empty "Test Store List.xlsx" file.
**Expected Behavior**: 
- Should return a DataFrame with only control stores
- Control stores should still have "Control" in the TestvControl column
- The pilot_df.xlsx file should be created with only control stores

### 4. Empty Store Codes File
**Description**: Test with an empty "Store Codes/{retailer}.xlsx" file.
**Expected Behavior**: 
- Should return a DataFrame with only test stores (if any match the activity_id)
- Test stores should have "Test" in the TestvControl column
- The pilot_df.xlsx file should be created with only test stores

### 5. Missing Columns in Test Store File
**Description**: Test with a "Test Store List.xlsx" file that is missing required columns.
**Expected Behavior**: 
- Should handle the error gracefully (either by raising an appropriate exception or returning an empty DataFrame)
- Should provide a meaningful error message

### 6. Missing Columns in Store Codes File
**Description**: Test with a "Store Codes/{retailer}.xlsx" file that is missing required columns.
**Expected Behavior**: 
- Should handle the error gracefully (either by raising an appropriate exception or returning a DataFrame with only test stores)
- Should provide a meaningful error message

### 7. Invalid Retailer
**Description**: Test with a retailer name that doesn't have a corresponding file in the Store Codes directory.
**Expected Behavior**: 
- Should handle the error gracefully (either by raising an appropriate exception or returning a DataFrame with only test stores)
- Should provide a meaningful error message

### 8. File Not Found
**Description**: Test when "Test Store List.xlsx" or "Store Codes/{retailer}.xlsx" files don't exist.
**Expected Behavior**: 
- Should raise an appropriate FileNotFoundError
- Should provide a meaningful error message

## Implementation Plan
The test file should be created at `tests/test_create_pilot_df.py` and follow the same structure as other test files in the project.

## Edge Cases to Consider
1. Test stores that are also in the control store list (should be excluded from control stores)
2. Large datasets that might affect performance
3. Special characters in retailer names
4. Date formats in the Excel files
5. Different data types in columns that should be consistent
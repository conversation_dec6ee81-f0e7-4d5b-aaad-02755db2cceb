import os
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error
import math


def mod1(store_df):
    """
    Process DataFrame data by transforming column names, handling negative values,
    converting dates, and reshaping the data structure.
    
    Args:
        store_df (pandas.DataFrame): Input DataFrame with 'POC SAPID', 'Sum(Value)', and 'Date' columns
        
    Returns:
        pandas.DataFrame: Processed DataFrame with POC_SAPID as index and dates as columns
    """
    data = store_df
    data['POC SAPID'] = data['POC SAPID'].astype(str)
    data = pd.concat([data, data], ignore_index=True)
    list_finaldata = list(data)
    mod_cols = [x.replace(' ', '_') for x in list_finaldata]
    data.columns = mod_cols
    data['Sum(Value)'] = data['Sum(Value)'].apply(lambda x: x if x >= 0 else 0)
    data['Date'] = pd.to_datetime(data['Date'], dayfirst=False)
    data['Year'] = pd.DatetimeIndex(data['Date']).year
    data['Month'] = pd.DatetimeIndex(data['Date']).month
    data['Month'] = data['Month'].astype('str')
    data['Month'] = data.Month.str.pad(2, side='left', fillchar='0')
    data = data.groupby(["POC_SAPID", "Month", "Date"]).agg({"Sum(Value)": "sum"}).reset_index()
    data_groupby = ['POC_SAPID']
    ident_cols = ['POC_SAPID', 'Month', 'Date']
    exp_cols = ['POC_SAPID']
    val_cols = ['Sum(Value)']
    molten_data = pd.melt(data, id_vars=ident_cols, value_vars=val_cols).sort_values(by=['POC_SAPID', 'Month', 'Date'])
    molten_data['MY'] = molten_data['Date'].astype('str')
    molten_data = molten_data.sort_values(by=['POC_SAPID', 'Month', 'Date'])
    Module1_data = molten_data.pivot(index='POC_SAPID', columns='MY', values='value')
    Module1_data.reset_index(inplace=True)
    return Module1_data


def filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit):
    """
    Filter control POCs based on RMSE similarity to test POC.
    
    Args:
        global_ctrl (pandas.DataFrame): DataFrame containing control POC data
        global_test (pandas.DataFrame): DataFrame containing test POC data
        min_index (int): Starting column index for baseline period
        activation_on (int): Column index for activation date
        limit (int): Maximum number of control POCs to return
        
    Returns:
        pandas.DataFrame: Filtered DataFrame of control POCs
    """
    #print("---------------------------------------------------------------------------")
    #print("Number of Control-POCs before process => "+str(len(global_ctrl)))
    #define the variables
    rmse_val = []
    control = global_ctrl
    ctrl_baseline = global_ctrl.iloc[:,min_index:activation_on]
    test_baseline = global_test.iloc[:,min_index:activation_on]
    #test dataframe
    predicted = test_baseline.iloc[0,:].tolist()
    
    for i in range(len(ctrl_baseline)):
        actual = ctrl_baseline.iloc[i,:].tolist()
        mse = mean_squared_error(actual, predicted)
        rmse = math.sqrt(mse)
        rmse_val.append(rmse)
        
    control["RMSE Value"] = rmse_val
    control["RMSE_Rank"] = control["RMSE Value"].rank(method='min')
    control = control[control["RMSE_Rank"]<=limit]
    
    new_ctrl = global_ctrl[global_ctrl["POC_ID"].isin(control["POC_ID"])]
    return new_ctrl


def create_val_data(sku, retailer):
    """
    Read and process SKU data from CSV files for a given retailer.
    
    This function performs the following operations:
    1. Constructs a path to SKU data based on the retailer
    2. Reads a CSV file for the specific SKU
    3. Renames several columns:
       - 'Store_Code' to 'POC SAPID'
       - 'Store Code applied by the retailer' to 'Store_Code'
       - 'Sales_Value' to 'Sum(Value)'
       - 'Sales_Units' to 'Sales_Units'
       - 'WEEK' to 'Date'
    4. Converts the 'Date' column to datetime
    5. Extracts year and month from the date
    6. Creates a 'Periods' column from year and month
    7. Returns the processed dataframe
    
    Args:
        sku (str): The SKU identifier
        retailer (str): The name of the retailer
        
    Returns:
        pandas.DataFrame: A DataFrame containing the processed SKU data
        
    Raises:
        FileNotFoundError: If the CSV file for the given SKU and retailer doesn't exist
        Exception: If there are issues reading or processing the data
    """
    # Get the global path (assuming it's set in the environment)
    E_path = os.environ.get('E_PATH', r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved")
    
    val_path = os.path.join(E_path, 'SKU DATA', f'{retailer}')
    store_df = pd.read_csv(val_path + f"\\{sku}.csv")
    print("Successful read")
    
    store_df.rename(columns={
        'Store_Code': 'POC SAPID',
        'Store Code applied by the retailer': 'Store_Code',
        'Sales_Value': 'Sum(Value)',
        'Sales_Units': 'Sales_Units',
        'WEEK': 'Date'
    }, inplace=True)
    
    # Ensure all expected columns are present
    expected_columns = ['Store_Code', 'Sales_Units']
    for col in expected_columns:
        if col not in store_df.columns:
            store_df[col] = pd.NA
    
    # Handle Date column if it exists
    if 'Date' in store_df.columns:
        store_df['Date'] = pd.to_datetime(store_df['Date'], errors='coerce')
        store_df['Year'] = store_df['Date'].dt.year
        store_df['Month'] = store_df['Date'].dt.month
        store_df['Periods'] = pd.to_datetime(store_df[['Year', 'Month']].assign(Day=1))
    else:
        # If Date column doesn't exist after renaming, create empty columns
        store_df['Date'] = pd.NaT
        store_df['Year'] = pd.NA
        store_df['Month'] = pd.NA
        store_df['Periods'] = pd.NaT
    
    return store_df
     
    return store_df

def prepare_and_clean_data(Base_data, meta):
    """
    Prepare and clean data for analysis by processing Base_data and meta DataFrames.
    
    This function performs the following operations:
    1. Cleans and processes Base_data POC_SAPID column
    2. Reads and merges data using read_data function
    3. Processes date columns and calculates min/max columns
    4. Restructures DATA DataFrame based on date ranges
    5. Cleans data by removing nulls and replacing with zeros
    6. Calculates average values and filters data
    7. Compares test and control data
    
    Args:
        Base_data (pandas.DataFrame): Raw base data containing POC information
        meta (pandas.DataFrame): Metadata containing campaign information
        
    Returns:
        tuple: A tuple containing:
            - DATA (pandas.DataFrame): Processed and cleaned main data
            - META (pandas.DataFrame): Processed metadata
            - testvscontrol_before (pandas.DataFrame): Comparison of test and control data
            - controlled (pandas.DataFrame): Filtered control data
            - test (pandas.DataFrame): Test data
            - min_index (int): Minimum index for data range
            - mid_index (int): Middle index for data range
            - max_index (int): Maximum index for data range
    """
    from utils import find_date, get_data_indices_n_years
    from data_cleaning import remove_nulls_by_threshold_in_range, replace_nulls_with_0
    from analysis import compare_test_control
    
    Base_data['POC_SAPID'] = pd.to_numeric(Base_data['POC_SAPID'], errors='coerce')
    Base_data = Base_data.dropna(subset=['POC_SAPID'])
    Base_data['POC_SAPID'] = Base_data['POC_SAPID'].astype('int')
    
    DATA, META = read_data(Base_data, meta)
    
    print(DATA['Test_Control'].unique())
    DATA.replace(0, np.nan, inplace=True)
    Test = DATA[DATA['Test_Control'] == 'Test']
    Control = DATA[DATA['Test_Control'] == 'Control']
    
    META["Start_Date"] = pd.to_datetime(META["Start_Date"])
    META["End_Date"] = pd.to_datetime(META["End_Date"])
    test_start_min_date = META["Start_Date"].min()
    test_end_max_date = META["End_Date"].max()
    min_column, max_column = find_date(test_start_min_date, test_end_max_date)
    
    RESTRICT_BASELINE_TO = 12
    Post_Analysis_DATA = pd.concat([DATA[["POC_ID"]], DATA.iloc[:, DATA.columns.get_loc(max_column)+1:DATA.shape[1]]], axis=1)
    Post_Analysis_DATA = Post_Analysis_DATA.loc[:, ~Post_Analysis_DATA.columns.duplicated()]
    
    if DATA.columns.get_loc(min_column) - RESTRICT_BASELINE_TO < 0:
        start = 0
    else:
        start = DATA.columns.get_loc(min_column) - RESTRICT_BASELINE_TO
        
    DATA = pd.concat([DATA[["POC_ID", "Start_Date", 'End_Date', 'Test_Control']], DATA.iloc[:, start:DATA.columns.get_loc(max_column)+1]], axis=1)
    DATA = DATA.loc[:, ~DATA.columns.duplicated()]
    
    mid_index = DATA.columns.get_loc(min_column)
    min_index, max_index, min_year, max_year = get_data_indices_n_years(list(Post_Analysis_DATA.columns))
    columns = list(Post_Analysis_DATA)
    Post_Analysis_DATA = replace_nulls_with_0(Post_Analysis_DATA, min_index, max_index, columns)
    
    min_index, max_index, min_year, max_year = get_data_indices_n_years(list(DATA.columns))
    columns = list(DATA)
    threshold = 70
    DATA = remove_nulls_by_threshold_in_range(DATA, threshold, min_index, mid_index)
    DATA = replace_nulls_with_0(DATA, min_index, max_index, columns)
    
    Test = DATA[DATA['Test_Control'] == 'Test']
    Control = DATA[DATA['Test_Control'] == 'Control']
    controlled = DATA[DATA['Test_Control'] == 'Control']
    test = DATA[DATA['Test_Control'] == 'Test']
    
    DATA['NET_AVG_Y1'] = DATA.iloc[:, min_index:mid_index].T.mean()
    DATA = DATA[DATA['NET_AVG_Y1'] != 0]
    controlled = DATA[DATA['Test_Control'] == 'Control']
    test = DATA[DATA['Test_Control'] == 'Test']
    
    DATA_backup = DATA.copy()
    mid_index = DATA_backup.columns.get_loc(min_column)
    DATA_backup["Baseline Period Avg"] = DATA_backup.iloc[:, min_index:mid_index].T.mean()
    DATA_backup["Promo Period Avg"] = DATA_backup.iloc[:, mid_index:-2].T.mean()
    
    controlled_backup = DATA_backup[DATA_backup['Test_Control'] == 'Control']
    test_backup = DATA_backup[DATA_backup['Test_Control'] == 'Test']
    controlled = controlled_backup
    test = test_backup
    
    controlled = controlled[controlled['Promo Period Avg'] != 0]
    controlled['dev'] = (controlled['Promo Period Avg'] - controlled['Baseline Period Avg']) / controlled['Baseline Period Avg']
    controlled = controlled[controlled['dev'] < 2]
    controlled.drop(['dev'], axis=1, inplace=True)
    
    testvscontrol_before = compare_test_control(controlled, test)
    
    return DATA, META, testvscontrol_before, controlled, test, min_index, mid_index, max_index

def read_data(Base_data,meta):
    DATA = Base_data    # From the Previous Module
    nd_cols = [x.replace(' ', '_') for x in DATA.columns]
    DATA.columns = nd_cols
    # print(DATA.columns.tolist())
    DATA = DATA.rename(columns={'POC_SAPID':'POC_ID'})
    DATA = DATA.dropna(subset=['POC_ID'])
    DATA.POC_ID = DATA.POC_ID.astype(int)
    DATA.POC_ID = DATA.POC_ID.astype(str)
    META=meta
     
    META = META.rename(columns={'Store number':'POC_ID','START DATE':'Start_Date','END DATE':'End_Date','TestvControl':'Test_Control'})
    META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
    print("Hi")
    print(META['Test_Control'].unique())
    # META.to_excel(E_path+"\\Meta_Test.xlsx")
    META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
    META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)
    nd_cols = [x.replace(' ', '_') for x in META.columns]
    META.columns = nd_cols
    META = META.dropna(subset=['POC_ID'])
    META.POC_ID = META.POC_ID.astype(int)
    META.POC_ID = META.POC_ID.astype(str)
     
    try:
        DATA = pd.merge(DATA, META[['POC_ID','Start_Date','End_Date','Test_Control']], on=['POC_ID'], how='right') #,'End_Date'
        d_cols = list(DATA)
        DATA.columns = d_cols
    except Exception as e:
        print("Error in mapping Activation Date to Volume-Data", e)
    return [DATA,META]
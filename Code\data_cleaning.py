import pandas as pd
import numpy as np

def remove_nulls_by_threshold_in_range(data, threshold, min_index, data_end_index):
    """
    Remove rows from a DataFrame based on the percentage of non-null values within a column range.
    
    Parameters:
    data (pd.DataFrame): The input DataFrame
    threshold (int): The minimum percentage of non-null values required (0-100)
    min_index (int): The starting column index for the range (inclusive)
    data_end_index (int): The ending column index for the range (exclusive)
    
    Returns:
    pd.DataFrame: A DataFrame with rows that meet the threshold criteria
    """
    data_sub = data.iloc[:, min_index:data_end_index].copy()
    _vol_thres = int(threshold * (data_end_index - min_index) / 100)
    data_sub = data_sub.dropna(thresh=_vol_thres)
    return data[data.index.isin(data_sub.index)]

def replace_nulls_with_0(data, min_index, end_index, columns):
    """
    Replace null values with 0 in a specified range of columns.
    
    Parameters:
    data (pd.DataFrame): The input DataFrame
    min_index (int): The starting column index for the range (inclusive)
    end_index (int): The ending column index for the range (inclusive)
    columns (list): List of column names corresponding to the DataFrame columns
    
    Returns:
    pd.DataFrame: A DataFrame with null values replaced by 0 in the specified column range
    """
    _vol_data_cols = [columns[x] for x in range(min_index, end_index+1)]
    # Filter out columns that don't exist in the DataFrame
    existing_cols = [col for col in _vol_data_cols if col in data.columns]
    if existing_cols:
        data[existing_cols] = data[existing_cols].replace({np.nan: 0})
    return data
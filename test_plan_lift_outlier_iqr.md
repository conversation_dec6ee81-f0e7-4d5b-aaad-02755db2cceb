# Test Plan for `lift_outlier_iqr` Function

## Overview
This document outlines the test plan for the `lift_outlier_iqr` function before and after modularization. The function is responsible for identifying outliers in the results data based on various criteria including lift thresholds, missing data, negative values, and insufficient control counts.

## Function Analysis
The `lift_outlier_iqr` function performs the following operations:
1. Takes a DataFrame `data` as input
2. Applies outlier detection based on a fixed threshold (300 for upper bound, -300 for lower bound) on the 'ABI-%Lift' column
3. Adds two new columns to the DataFrame:
   - 'Outlier': "Yes" if the value is beyond thresholds or if certain conditions are met, "No" otherwise
   - 'Outlier_Reason': A text description of why a row was marked as an outlier
4. Checks for various conditions that would mark a row as an outlier:
   - Values beyond the fixed thresholds (300/-300)
   - NaN values in several key columns
   - Negative values in several key columns
   - Control count below 5

## Dependencies
The function depends on specific column names in the DataFrame:
- 'ABI-%Lift'
- 'ABI-Test analysis period'
- 'ABI-Control analysis period'
- 'ABI-Test baseline period'
- 'ABI-Control baseline period'
- 'ABI-Control count'

## Test Cases

### 1. Normal Case
**Description**: Test the function with a typical DataFrame that has a mix of outliers and non-outliers.
**Input**: DataFrame with various values in the required columns
**Expected Behavior**: 
- Rows with 'ABI-%Lift' values between -300 and 300 should be marked as "No" for Outlier
- Rows with 'ABI-%Lift' values outside this range should be marked as "Yes" for Outlier with reason "Uplift beyond threshold"
- The function should add 'Outlier' and 'Outlier_Reason' columns to the DataFrame
- The function should return the modified DataFrame

### 2. All Values Within Thresholds
**Description**: Test with a DataFrame where all 'ABI-%Lift' values are within the -300 to 300 range.
**Input**: DataFrame with 'ABI-%Lift' values between -300 and 300
**Expected Behavior**: 
- All rows should be marked as "No" for Outlier
- 'Outlier_Reason' should be empty for all rows
- No other outlier conditions should be triggered

### 3. All Values Beyond Thresholds
**Description**: Test with a DataFrame where all 'ABI-%Lift' values are beyond the thresholds.
**Input**: DataFrame with 'ABI-%Lift' values all > 300 or all < -300
**Expected Behavior**: 
- All rows should be marked as "Yes" for Outlier
- 'Outlier_Reason' should be "Uplift beyond threshold" for all rows
- No other outlier conditions should be triggered (unless data also meets those criteria)

### 4. Missing Columns
**Description**: Test with a DataFrame that is missing required columns.
**Input**: DataFrame missing one or more of the required columns
**Expected Behavior**: 
- Should raise a KeyError or handle gracefully by skipping those checks
- Should provide a meaningful error message or log warning

### 5. All Outliers Due to NaN Values
**Description**: Test with a DataFrame where all rows have NaN values in key columns.
**Input**: DataFrame where all rows have NaN in 'ABI-Test analysis period', 'ABI-Control analysis period', etc.
**Expected Behavior**: 
- All rows should be marked as "Yes" for Outlier
- 'Outlier_Reason' should indicate the specific reason for each NaN column
- Rows with NaN in 'ABI-Test analysis period' should have reason "Test site has zero-valued in the analysis period"

### 6. All Outliers Due to Negative Values
**Description**: Test with a DataFrame where all rows have negative values in key columns.
**Input**: DataFrame where all rows have negative values in 'ABI-Test analysis period', 'ABI-Control analysis period', etc.
**Expected Behavior**: 
- All rows should be marked as "Yes" for Outlier
- 'Outlier_Reason' should indicate the specific reason for each negative value
- Rows with negative values in 'ABI-Test analysis period' should have reason "Test site has negative data in the analysis period"

### 7. Control Count Edge Cases
**Description**: Test with various control count values.
**Input**: DataFrame with 'ABI-Control count' values of 0, 1, 4, 5, 6, NaN
**Expected Behavior**: 
- Rows with 'ABI-Control count' < 5 should be marked as "Yes" for Outlier with reason "Test site has control POCs below 5"
- Rows with 'ABI-Control count' >= 5 should be marked as "No" for Outlier
- Rows with NaN in 'ABI-Control count' should be marked as "Yes" for Outlier with reason "Test site has control POCs below 5"

### 8. Mixed Conditions
**Description**: Test with a DataFrame that has rows meeting different outlier criteria.
**Input**: DataFrame with some rows having extreme lift values, some with NaN values, some with negative values, and some normal values
**Expected Behavior**: 
- Each row should be correctly classified based on its specific conditions
- 'Outlier_Reason' should accurately reflect why each row was marked as an outlier

### 9. Empty DataFrame
**Description**: Test with an empty DataFrame.
**Input**: Empty DataFrame with the required column structure
**Expected Behavior**: 
- Should return the same empty DataFrame with 'Outlier' and 'Outlier_Reason' columns added
- Should not raise any exceptions

### 10. Single Row DataFrame
**Description**: Test with a DataFrame containing only one row.
**Input**: DataFrame with a single row of data
**Expected Behavior**: 
- Should correctly classify the single row based on its values
- Should add the required columns and return the modified DataFrame

## Implementation Plan
The test file should be created at `tests/test_lift_outlier_iqr.py` and follow the same structure as other test files in the project.

## Edge Cases to Consider
1. DataFrames with mixed data types in columns
2. Very large datasets that might affect performance
3. Special float values (inf, -inf)
4. Unicode or special characters in data
5. Date columns that might interfere with numeric operations
6. Duplicate column names
7. Case sensitivity in column names (if applicable)
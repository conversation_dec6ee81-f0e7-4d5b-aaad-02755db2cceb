import os
import pandas as pd
from data_retrieval import create_pilot_df


def initialize_accelerate(activity_id, retailer, start_date, end_date, campaign):
    """
    Initialize the accelerate function by setting up directories and metadata.
    
    This function creates the necessary directory structure, generates metadata
    from the pilot data, and saves it to a CSV file for use in the analysis.
    
    Args:
        activity_id (str): The activity/campaign ID
        retailer (str): The name of the retailer
        start_date (datetime): The start date of the analysis period
        end_date (datetime): The end date of the analysis period
        campaign (str): The campaign name
        
    Returns:
        pandas.DataFrame: The metadata DataFrame with start and end dates
    """
    # Get the global path
    E_path = os.environ.get('E_PATH', r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved")
    
    # Create META directory if it doesn't exist
    target_path = os.path.join(E_path, 'META')
    if not os.path.isdir(target_path):
        os.makedirs(target_path, exist_ok=True)
    
    # Create and process metadata
    meta = create_pilot_df(activity_id, retailer)
    meta['START DATE'] = start_date
    meta['END DATE'] = end_date
    meta.rename(columns={'Store_Id': 'POC_ID'}, inplace=True)
    meta.to_csv(target_path + f"\\Meta_{activity_id}.csv", index=False)
    
    print(campaign, activity_id, retailer)
    
    return meta


def aggregate_results_output(final_result_output, APT_RESULTS, test_control_list, activity_id, sku, E_path):
    """
    Aggregate results and output to Excel files
    
    Args:
        final_result_output (pd.DataFrame): Accumulated results DataFrame
        APT_RESULTS (pd.DataFrame): Current SKU's results
        test_control_list (pd.DataFrame): Test vs Control mapping
        activity_id (str): Activity ID
        sku (str): SKU identifier
        E_path (str): Base directory path
        
    Returns:
        pd.DataFrame: Updated final_result_output
    """
    # Aggregate results
    updated_output = pd.concat([final_result_output, APT_RESULTS], ignore_index=True)
    
    # Save test-control mapping
    test_control_list.to_excel(
        os.path.join(E_path, "TestvCtrl", f"{activity_id}.xlsx"),
        sheet_name="Control_Mapping",
        index=False
    )
    
    return updated_output
import unittest
import pandas as pd
import numpy as np
import sys
import os
from unittest.mock import patch, Mock

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

# Import the function to be tested
from analysis import get_dist_mat_grp

class TestGetDistMatGrp(unittest.TestCase):
    
    def setUp(self):
        """Set up test data"""
        # Mock the dtw.distance_matrix function to avoid heavy computation
        self.dtw_patcher = patch('analysis.dtw.distance_matrix')
        self.mock_dtw = self.dtw_patcher.start()
        
        # Mock time.time to return consistent values
        self.time_patcher = patch('analysis.time.time')
        self.mock_time = self.time_patcher.start()
        self.mock_time.return_value = 0
        
        # Mock print to capture output
        self.print_patcher = patch('builtins.print')
        self.mock_print = self.print_patcher.start()
    
    def tearDown(self):
        """Clean up mocks"""
        self.dtw_patcher.stop()
        self.time_patcher.stop()
        self.print_patcher.stop()
    
    def test_normal_case(self):
        """Test with typical input data containing numeric values"""
        # Create sample DataFrames
        df1 = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [4, 5, 6],
            'C': [7, 8, 9]
        })
        
        df2 = pd.DataFrame({
            'A': [10, 11],
            'B': [12, 13],
            'C': [14, 15]
        })
        
        dm_data_grp = [df1, df2]
        
        # Configure mock to return sample distance matrices
        self.mock_dtw.side_effect = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]]),  # 3x3 matrix for df1
            np.array([[0, 3], [3, 0]])  # 2x2 matrix for df2
        ]
        
        # Call the function
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is a list
        self.assertIsInstance(result, list)
        
        # Check that we have the expected number of matrices
        self.assertEqual(len(result), 2)
        
        # Check that each element is a numpy array
        for matrix in result:
            self.assertIsInstance(matrix, np.ndarray)
        
        # Check the shape of each matrix
        self.assertEqual(result[0].shape, (3, 3))
        self.assertEqual(result[1].shape, (2, 2))
        
        # Check that all values are finite (no NaN or Inf)
        for matrix in result:
            self.assertTrue(np.isfinite(matrix).all())
        
        # Check that NaN and Inf values are replaced with 0
        # Configure mock to return matrix with NaN and Inf values
        self.mock_dtw.side_effect = [
            np.array([[0, np.nan, 2], [np.inf, 0, 1], [2, 1, 0]])
        ]
        
        dm_data_grp = [df1]
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that NaN and Inf values are replaced with 0
        self.assertEqual(result[0][0, 1], 0)  # Was NaN
        self.assertEqual(result[0][1, 0], 0)  # Was Inf
    
    def test_empty_list(self):
        """Test with an empty list as input"""
        dm_data_grp = []
        
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is an empty list
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)
        
        # Check that no distance matrix calculation was attempted
        self.mock_dtw.assert_not_called()
    
    def test_single_dataframe_group(self):
        """Test with a single DataFrame in the list"""
        df = pd.DataFrame({
            'A': [1, 2, 3, 4],
            'B': [5, 6, 7, 8],
            'C': [9, 10, 11, 12]
        })
        
        dm_data_grp = [df]
        
        # Configure mock to return sample distance matrix
        self.mock_dtw.return_value = np.array([
            [0, 1, 2, 3],
            [1, 0, 1, 2],
            [2, 1, 0, 1],
            [3, 2, 1, 0]
        ])
        
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is a list with one element
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Check that the element is a numpy array
        self.assertIsInstance(result[0], np.ndarray)
        
        # Check the shape of the matrix
        self.assertEqual(result[0].shape, (4, 4))
        
        # Check that all values are finite
        self.assertTrue(np.isfinite(result[0]).all())
    
    def test_dataframe_with_all_zeros(self):
        """Test with DataFrames containing all zero values"""
        df = pd.DataFrame({
            'A': [0, 0, 0],
            'B': [0, 0, 0],
            'C': [0, 0, 0]
        })
        
        dm_data_grp = [df]
        
        # Configure mock to return matrix with zeros
        self.mock_dtw.return_value = np.array([
            [0, 0, 0],
            [0, 0, 0],
            [0, 0, 0]
        ])
        
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is a list with one element
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Check that the matrix contains zeros
        self.assertTrue(np.all(result[0] == 0))
    
    def test_exception_handling(self):
        """Test that exceptions are handled properly"""
        df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [4, 5, 6],
            'C': [7, 8, 9]
        })
        
        dm_data_grp = [df]
        
        # Configure mock to raise an exception
        self.mock_dtw.side_effect = Exception("Test exception")
        
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is an empty list (exception occurred, nothing appended)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 0)
        
        # Check that error message was printed
        self.mock_print.assert_called()  # Error messages should be printed
    
    def test_multiple_groups_with_mixed_validity(self):
        """Test with a mix of valid and invalid DataFrames"""
        df1 = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [4, 5, 6],
            'C': [7, 8, 9]
        })
        
        df2 = pd.DataFrame({
            'A': [10, 11],
            'B': [12, 13],
            'C': [14, 15]
        })
        
        dm_data_grp = [df1, df2]
        
        # Configure mock to return valid matrix for first call and exception for second
        self.mock_dtw.side_effect = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]]),  # Valid for df1
            Exception("Test exception")  # Exception for df2
        ]
        
        result = get_dist_mat_grp(dm_data_grp)
        
        # Check that result is a list with one element (only the first group succeeded)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        
        # Check that the first matrix is valid
        self.assertIsInstance(result[0], np.ndarray)
        self.assertEqual(result[0].shape, (3, 3))

if __name__ == '__main__':
    unittest.main()
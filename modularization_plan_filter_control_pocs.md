# Modularization Plan for `filter_control_pocs` Function

## Overview
This plan details the process of moving the `filter_control_pocs` function from `Code/Accelerate.py` to `Code/data_processing.py` to improve code organization and maintainability.

## Current State
- The `filter_control_pocs` function is currently defined in `Code/Accelerate.py` at lines 146-168
- It depends on `sklearn.metrics.mean_squared_error` and `math.sqrt`
- It is called from within `Accelerate.py` at line 675

## Target State
- The `filter_control_pocs` function will be moved to `Code/data_processing.py`
- It will be imported and used in `Code/Accelerate.py`
- All functionality will remain the same

## Implementation Steps

### 1. Update data_processing.py
Add the `filter_control_pocs` function to `Code/data_processing.py`:

```python
def filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit):
    """
    Filter control POCs based on RMSE similarity to test POC.
    
    Args:
        global_ctrl (pandas.DataFrame): DataFrame containing control POC data
        global_test (pandas.DataFrame): DataFrame containing test POC data
        min_index (int): Starting column index for baseline period
        activation_on (int): Column index for activation date
        limit (int): Maximum number of control POCs to return
        
    Returns:
        pandas.DataFrame: Filtered DataFrame of control POCs
    """
    #print("---------------------------------------------------------------------------")
    #print("Number of Control-POCs before process => "+str(len(global_ctrl)))
    #define the variables
    rmse_val = []
    control = global_ctrl
    ctrl_baseline = global_ctrl.iloc[:,min_index:activation_on]
    test_baseline = global_test.iloc[:,min_index:activation_on]
    #test dataframe
    predicted = test_baseline.iloc[0,:].tolist()
    
    for i in range(len(ctrl_baseline)):
        actual = ctrl_baseline.iloc[i,:].tolist()
        mse = mean_squared_error(actual, predicted)
        rmse = math.sqrt(mse)
        rmse_val.append(rmse)
        
    control["RMSE Value"] = rmse_val
    control["RMSE_Rank"] = control["RMSE Value"].rank(method='min')
    control = control[control["RMSE_Rank"]<=limit]
    
    new_ctrl = global_ctrl[global_ctrl["POC_ID"].isin(control["POC_ID"])]
    return new_ctrl
```

### 2. Add Required Imports to data_processing.py
Add the following imports to the top of `Code/data_processing.py`:

```python
from sklearn.metrics import mean_squared_error
import math
```

These should be placed after the existing imports but before the function definitions.

### 3. Update Accelerate.py
Remove the `filter_control_pocs` function from `Code/Accelerate.py` and update the import statement to include the function:

Change line 39 from:
```python
from data_processing import create_val_data, mod1, read_data
```

To:
```python
from data_processing import create_val_data, mod1, read_data, filter_control_pocs
```

### 4. Verify Function Calls
Ensure that all calls to `filter_control_pocs` in `Accelerate.py` remain unchanged since the function will be imported with the same name.

The function is called at line 675:
```python
global_ctrl = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
```

This call will work unchanged since we're importing the function with the same name.

## Dependencies
- `sklearn.metrics.mean_squared_error` (will be imported in data_processing.py)
- `math.sqrt` (will be imported in data_processing.py)
- pandas DataFrame operations (already available through existing imports)

## Testing Considerations
- The function should work identically after modularization
- All existing functionality should be preserved
- The function should be properly imported and accessible in Accelerate.py

## Rollback Plan
If issues arise during modularization:
1. Revert changes to data_processing.py
2. Restore the filter_control_pocs function to Accelerate.py
3. Revert the import statement in Accelerate.py

## Verification Steps
1. Run the application to ensure no import errors
2. Verify that the function produces the same results as before
3. Check that all calls to the function work correctly
4. Run any existing tests to ensure no regressions
import unittest
import pandas as pd
import os
import sys
import tempfile
import numpy as np
from unittest.mock import patch, MagicMock

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_retrieval import get_sku_list, create_pilot_df

class TestGetSkuList(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create a sample SKU list CSV for testing
        self.sample_data = pd.DataFrame({
            'Retailer': ['Asda', 'Asda', 'Morrisons', 'Morrisons', 'Tesco'],
            'BRAND': ['Budweiser', 'Corona', 'Budweiser', 'Corona', 'Stella Artois'],
            'ITEM CODE': ['2000024510778', '2000015054018', '2000041753102', '2000015054018', '20004612173']
        })
        
        self.sample_csv_path = os.path.join(self.test_dir, 'SKU List.csv')
        self.sample_data.to_csv(self.sample_csv_path, index=False)
        
        # Store original path and update for testing
        self.original_path = os.environ.get('E_PATH', '')
        os.environ['E_PATH'] = self.test_dir
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.test_dir)
        # Restore original path
        if self.original_path:
            os.environ['E_PATH'] = self.original_path
        else:
            os.environ.pop('E_PATH', None)
    
    def test_normal_case(self):
        """Test normal case with matching retailer and brand"""
        result = get_sku_list('Asda', 'Budweiser')
        self.assertEqual(len(result), 1)
        self.assertIn(2000024510778, result['ITEM CODE'].values)
    
    def test_no_matching_retailer_brand(self):
        """Test case with no matching retailer/brand combination"""
        result = get_sku_list('Asda', 'NonExistentBrand')
        self.assertEqual(len(result), 0)
        
        result = get_sku_list('NonExistentRetailer', 'Budweiser')
        self.assertEqual(len(result), 0)
    
    def test_empty_csv_file(self):
        """Test case with empty CSV file"""
        # Create empty CSV file
        empty_csv_path = os.path.join(self.test_dir, 'SKU List.csv')
        pd.DataFrame(columns=['Retailer', 'BRAND', 'ITEM CODE']).to_csv(empty_csv_path, index=False)
        
        result = get_sku_list('Asda', 'Budweiser')
        self.assertEqual(len(result), 0)
    
    def test_csv_with_missing_columns(self):
        """Test case with CSV file missing required columns"""
        # Create CSV with missing columns
        bad_csv_path = os.path.join(self.test_dir, 'SKU List.csv')
        pd.DataFrame({'Retailer': ['Asda'], 'BRAND': ['Budweiser']}).to_csv(bad_csv_path, index=False)
        
        # Should handle gracefully (might be empty result or error)
        try:
            result = get_sku_list('Asda', 'Budweiser')
            # If it doesn't raise an error, result should be empty or handle gracefully
            self.assertIsInstance(result, pd.DataFrame)
        except Exception as e:
            # If it raises an error, that's also acceptable behavior
            self.assertIsInstance(e, (KeyError, ValueError))
    
    def test_case_sensitivity(self):
        """Test case sensitivity of retailer and brand parameters"""
        # Test with different case
        result = get_sku_list('asda', 'budweiser')
        self.assertEqual(len(result), 0)  # Should be case sensitive


class TestCreatePilotDf(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create sample Test Store List.xlsx
        self.test_store_data = pd.DataFrame({
            'Campaign_ID': ['ACT123', 'ACT123', 'ACT456'],
            'Store_Id': [1001, 1002, 1003],
            'Other_Column': ['A', 'B', 'C']
        })
        
        # Create sample store codes files
        self.asda_store_data = pd.DataFrame({
            'Store code applied by the retailer': [1001, 1002, 1004, 1005, 1006]
        })
        
        self.tesco_store_data = pd.DataFrame({
            'Store code applied by the retailer': [2001, 2002, 2004, 2005, 2006]
        })
        
        # Create directories
        os.makedirs(os.path.join(self.test_dir, 'Store Codes'), exist_ok=True)
        
        # Save sample files
        self.test_store_data.to_excel(os.path.join(self.test_dir, 'Test Store List.xlsx'), index=False)
        self.asda_store_data.to_excel(os.path.join(self.test_dir, 'Store Codes', 'Asda.xlsx'), index=False)
        self.tesco_store_data.to_excel(os.path.join(self.test_dir, 'Store Codes', 'Tesco.xlsx'), index=False)
        
        # Store original path and update for testing
        self.original_path = os.environ.get('E_PATH', '')
        os.environ['E_PATH'] = self.test_dir
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.test_dir)
        # Restore original path
        if self.original_path:
            os.environ['E_PATH'] = self.original_path
        else:
            os.environ.pop('E_PATH', None)
    
    @patch('data_retrieval.pd.DataFrame.to_excel')
    def test_create_pilot_df_normal_case(self, mock_to_excel):
        """Test normal case with valid activity_id and retailer"""
        result = create_pilot_df('ACT123', 'Asda')
        
        # Check that the function returns a DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        
        # Check that the result contains both test and control stores
        self.assertIn('TestvControl', result.columns)
        test_stores = result[result['TestvControl'] == 'Test']
        control_stores = result[result['TestvControl'] == 'Control']
        
        # Should have 2 test stores (from Test Store List)
        self.assertEqual(len(test_stores), 2)
        # Should have 3 control stores (5 total Asda stores - 2 test stores)
        self.assertEqual(len(control_stores), 3)
        
        # Check that all test store IDs are present
        self.assertIn(1001, test_stores['Store_Id'].values)
        self.assertIn(1002, test_stores['Store_Id'].values)
        
        # Check that control stores don't include test stores
        control_store_ids = control_stores['Store_Id'].values
        self.assertNotIn(1001, control_store_ids)
        self.assertNotIn(1002, control_store_ids)
        
        # Check that campaign ID is correctly set
        self.assertTrue((result['Campaign_ID'] == 'ACT123').all())
        
        # Check that to_excel was called
        mock_to_excel.assert_called_once()
    
    def test_create_pilot_df_no_test_stores(self):
        """Test case where no test stores exist for given activity_id"""
        result = create_pilot_df('NONEXISTENT', 'Asda')
        
        # Should still return a DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        
        # Should have 0 test stores and 5 control stores
        test_stores = result[result['TestvControl'] == 'Test']
        control_stores = result[result['TestvControl'] == 'Control']
        
        self.assertEqual(len(test_stores), 0)
        self.assertEqual(len(control_stores), 5)  # All Asda stores become control
        
        # Check that campaign ID is correctly set
        self.assertTrue((result['Campaign_ID'] == 'NONEXISTENT').all())
    
    def test_create_pilot_df_invalid_retailer(self):
        """Test case with invalid retailer"""
        # Should raise FileNotFoundError since the retailer file doesn't exist
        with self.assertRaises(FileNotFoundError):
            create_pilot_df('ACT123', 'InvalidRetailer')
    
    def test_create_pilot_df_empty_store_list(self):
        """Test case with empty store list for retailer"""
        # Create empty store list file
        empty_store_data = pd.DataFrame(columns=['Store code applied by the retailer'])
        empty_store_data.to_excel(os.path.join(self.test_dir, 'Store Codes', 'Asda.xlsx'), index=False)
        
        result = create_pilot_df('ACT123', 'Asda')
        
        # Should still return a DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        
        # Should have 2 test stores and 0 control stores
        test_stores = result[result['TestvControl'] == 'Test']
        control_stores = result[result['TestvControl'] == 'Control']
        
        self.assertEqual(len(test_stores), 2)
        self.assertEqual(len(control_stores), 0)

if __name__ == '__main__':
    unittest.main()
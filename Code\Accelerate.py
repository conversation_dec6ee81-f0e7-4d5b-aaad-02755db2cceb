import os
import pandas as pd
import numpy as np
import warnings
from scipy.stats import wil<PERSON>xon, ttest_ind, ttest_rel, f_oneway
from dtaidistance import dtw, clustering
from sklearn.linear_model import LinearRegression
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

from utils import find_date, get_activation_week_index, get_end_week_index
from data_cleaning import remove_nulls_by_threshold_in_range, replace_nulls_with_0
from analysis import compare_test_control, get_clustered_data, get_dist_mat_grp, get_optimal_n_cluster, get_uplift, significance_level, lift_outlier_iqr, get_uplift_val
from data_retrieval import get_sku_list, create_pilot_df
from data_processing import create_val_data, mod1, prepare_and_clean_data
from orchestration import initialize_accelerate
from analysis import process_uplift_calculation
pd.set_option('display.max_columns',None)
pd.set_option('display.max_rows',None)
pd.set_option('display.float_format','{:.2f}'.format)
warnings.filterwarnings('ignore')

global path
E_path=r"C:\Users\<USER>\OneDrive - Anheuser-Busch InBev\Documents\Accelerate Improved"
final_result_output=pd.DataFrame()
activity_df = pd.read_excel(E_path+"\\Activity_ID_List.xlsx")
# activity_df = pd.read_excel(E_path+"\\Act_ID_ASDA1.xlsx")





def accelerate(start_date, end_date, brand, activity_id, campaign, retailer):
    global final_result_output
    meta = initialize_accelerate(activity_id, retailer, start_date, end_date, campaign)
    sku_list = get_sku_list(retailer,brand)
    for sku in sku_list["ITEM CODE"]:
        print("sku :",sku)
        try :
            store_df = create_val_data(sku,retailer)
            Base_data = mod1(store_df)
            # Prepare and clean data using the modularized function
            DATA, META, testvscontrol_before, controlled, test, min_index, mid_index, max_index = prepare_and_clean_data(Base_data, meta)
            RESTRICT_BASELINE_TO = 12
# graph
            save_path = os.path.join(E_path,"Plots",f"{activity_id}.png")
            plt.rcParams['figure.figsize']=[18,8]
            plt.plot(testvscontrol_before.T,marker='o')
            plt.xticks(rotation=90)
            plt.legend()
            plt.savefig(save_path, bbox_inches="tight", dpi=300)  
            plt.close()
# graph
            data_live = pd.concat([controlled, test])
            data_live = data_live.sort_values(by=['NET_AVG_Y1'])
            test_live = test.sort_values(by=['NET_AVG_Y1'])
            data_live = data_live[data_live.NET_AVG_Y1 != 0]
            test_live = test_live[test_live.NET_AVG_Y1 != 0]
            
            # Perform uplift calculation using modularized function
            test_control_list, APT_RESULTS = process_uplift_calculation(
                data_live, test_live, DATA, min_index, max_index,
                activity_id, sku, E_path, campaign, retailer
            )
            
            APT_Outlier = lift_outlier_iqr(APT_RESULTS)
            sig_level = significance_level(APT_Outlier, APT_RESULTS, sku)
            
            # Aggregate results directly without intermediate steps
            final_result_output = pd.concat([final_result_output, APT_RESULTS])
            
        except Exception as e:
            print(f"Error processing SKU {sku}: {str(e)}")
            continue
    final_result_output.to_excel(E_path+"\\Results"+f"\\Final_Results_{activity_id}.xlsx",sheet_name="Results", index=False)

for _, row in activity_df.iterrows():
    accelerate(row['Nielsen Start'], row['Nielsen End'], row['Brand'], row['Activity UID'],row['Campaign'],row['Retailer'])
    
final_result_output.to_excel(os.path.join(E_path, "Results", f"Final_Results_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx"), sheet_name="Results", index=False)

print("The End")
from datetime import datetime
current_datetime = datetime.now()
formatted_timestamp = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
print(f"Formatted Timestamp: {formatted_timestamp}")
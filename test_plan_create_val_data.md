# Test Plan for `create_val_data` Function

## Overview
This document outlines the test plan for the `create_val_data` function before and after modularization. The function is responsible for reading and processing SKU data from CSV files for a given retailer.

## Function Analysis
The `create_val_data` function performs the following operations:
1. Takes `sku` and `retailer` as parameters
2. Constructs a path to SKU data based on the retailer
3. Reads a CSV file for the specific SKU
4. Renames several columns:
   - 'Store_Code' to 'POC SAPID'
   - 'Store Code applied by the retailer' to 'Store_Code'
   - 'Sales_Value' to 'Sum(Value)'
   - 'Sales_Units' to 'Sales_Units'
   - 'WEEK' to 'Date'
5. Converts the 'Date' column to datetime
6. Extracts year and month from the date
7. Creates a 'Periods' column from year and month
8. Returns the processed dataframe

## Test Cases

### 1. Normal Case
**Description**: Test the function with valid sku and retailer that have matching data.
**Expected Behavior**: 
- Should return a DataFrame with the processed data
- Columns should be renamed correctly
- Date column should be converted to datetime
- Year and Month columns should be extracted correctly
- Periods column should be created correctly

### 2. Non-existent File
**Description**: Test with a sku/retailer combination that doesn't have a corresponding CSV file.
**Expected Behavior**: 
- Should raise an appropriate FileNotFoundError
- Should provide a meaningful error message

### 3. Empty CSV File
**Description**: Test with an empty CSV file.
**Expected Behavior**: 
- Should handle gracefully (might return an empty DataFrame or raise an appropriate exception)
- Should provide a meaningful error message if an exception is raised

### 4. Missing Columns in CSV File
**Description**: Test with a CSV file that is missing required columns.
**Expected Behavior**: 
- Should handle gracefully (might return a DataFrame with missing columns or raise an appropriate exception)
- Should provide a meaningful error message if an exception is raised

### 5. Invalid Date Formats
**Description**: Test with a CSV file that has invalid date formats in the WEEK column.
**Expected Behavior**: 
- Should handle invalid dates gracefully (coerce to NaT or raise an appropriate exception)
- Should provide a meaningful error message if an exception is raised

### 6. Invalid Retailer
**Description**: Test with a retailer name that doesn't have a corresponding directory.
**Expected Behavior**: 
- Should raise an appropriate FileNotFoundError
- Should provide a meaningful error message

### 7. Special Characters in SKU
**Description**: Test with SKU values that contain special characters.
**Expected Behavior**: 
- Should handle special characters in SKU values correctly
- Should construct the file path correctly

## Edge Cases to Consider
1. CSV files with different encodings
2. Very large CSV files that might affect performance
3. CSV files with inconsistent data types in columns
4. Date values that are at the boundaries of valid date ranges
5. SKU values with leading/trailing whitespace
6. Retailer names with special characters or spaces

## Implementation Plan
The test file should be created at `tests/test_create_val_data.py` and follow the same structure as other test files in the project.
# Test Plan for `get_clustered_data` Function

## Function Overview
The `get_clustered_data` function performs clustering on data groups using KMeans algorithm. It takes distance matrices, number of clusters, dates, POC IDs, and original data to produce clustered data groups.

## Function Signature
```python
def get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA):
```

## Parameters
- `dist_mat_grp`: List of distance matrices for each data group
- `num_clusters_grp`: List of optimal number of clusters for each data group
- `date_grp`: List of dates for each data group
- `poc_ids_grp`: List of POC IDs for each data group
- `kmeans_grp`: List to store KMeans models (output parameter)
- `DATA`: Original data DataFrame

## Expected Behavior
1. For each distance matrix in `dist_mat_grp`:
   - Fit a KMeans model with the specified number of clusters
   - Store the model in `kmeans_grp`
   - Predict cluster labels for the data
   - Filter original `DATA` to include only POC IDs for this group
   - Add cluster labels to the filtered data
   - Append the clustered data to `clustered_data_grp`

2. Return `clustered_data_grp` containing clustered data for all groups

## Test Cases

### Normal Cases
1. **Standard Input**: Test with typical inputs that match the expected structure
   - Multiple data groups with valid distance matrices
   - Valid number of clusters for each group
   - Consistent date and POC ID groups
   - Non-empty DATA DataFrame

2. **Single Group**: Test with a single data group
   - One distance matrix
   - One value in num_clusters_grp
   - One date group and one POC ID group

### Edge Cases
1. **Empty Lists**: Test with empty input lists
   - All input lists are empty
   - Should return an empty list

2. **Mismatched List Lengths**: Test with inconsistent list lengths
   - dist_mat_grp has 3 elements but num_clusters_grp has 2
   - Should handle gracefully or raise appropriate error

3. **Invalid Distance Matrices**: Test with problematic distance matrices
   - Matrix with NaN values
   - Matrix with infinite values
   - Empty matrix
   - Non-square matrix

4. **Zero Clusters**: Test with 0 clusters specified
   - May cause KMeans to fail

5. **Large Number of Clusters**: Test with more clusters than data points
   - Should handle gracefully or raise appropriate error

6. **Missing POC IDs**: Test with POC IDs that don't exist in DATA
   - Should handle gracefully with empty or partial results

7. **Empty DATA DataFrame**: Test with completely empty original data
   - Should handle gracefully

### Error Conditions
1. **Invalid Data Types**: Test with incorrect data types
   - Non-list inputs
   - Non-numeric values where numeric expected
   - Non-DataFrame DATA parameter

2. **None Values**: Test with None values in parameters
   - Should handle gracefully or raise appropriate error

## Dependencies
- sklearn.cluster.KMeans
- pandas DataFrame operations
- numpy array operations

## Assertions
1. Output should be a list
2. Each element in the output list should be a DataFrame
3. Each DataFrame should contain a 'Cluster' column with integer values
4. Each DataFrame should only contain rows with POC_IDs from the corresponding poc_ids_grp
5. The kmeans_grp parameter should be populated with KMeans models
6. The number of elements in output should match the number of input groups

## Test Implementation

The test file should be created at `tests/test_get_clustered_data.py` and follow the structure of existing test files in the project:

```python
import unittest
import pandas as pd
import numpy as np
import sys
import os
from unittest.mock import Mock, patch

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

# Import the function to be tested
from analysis import get_clustered_data

class TestGetClusteredData(unittest.TestCase):
    
    def setUp(self):
        """Set up test data"""
        # Create sample DATA DataFrame
        self.sample_data = pd.DataFrame({
            'POC_ID': [1, 2, 3, 4, 5, 6],
            'Test_Control': ['Test', 'Control', 'Control', 'Test', 'Control', 'Test'],
            '2023-01-01': [10, 20, 30, 40, 50, 60],
            '2023-01-08': [15, 25, 35, 45, 55, 65],
            '2023-01-15': [12, 22, 32, 42, 52, 62]
        })
    
    def test_normal_case(self):
        """Test with typical inputs"""
        # Create sample distance matrices
        dist_mat_grp = [
            np.array([[0, 1, 2], [1, 0, 1], [2, 1, 0]]),
            np.array([[0, 3], [3, 0]])
        ]
        
        # Number of clusters for each group
        num_clusters_grp = [2, 1]
        
        # Date groups (not used in function but needed for signature)
        date_grp = [
            pd.Series(['2023-01-01', '2023-01-01', '2023-01']),
            pd.Series(['2023-01-01', '2023-01-01'])
        ]
        
        # POC ID groups
        poc_ids_grp = [
            pd.Series([1, 2, 3]),  # POC IDs for first group
            pd.Series([4, 5])      # POC IDs for second group
        ]
        
        # KMeans models list (output parameter)
        kmeans_grp = []
        
        # Call the function
        result = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, self.sample_data)
        
        # Check that result is a list
        self.assertIsInstance(result, list)
        
        # Check that we have the expected number of groups
        self.assertEqual(len(result), 2)
        
        # Check that each element is a DataFrame
        for group in result:
            self.assertIsInstance(group, pd.DataFrame)
        
        # Check that each DataFrame has a Cluster column
        for group in result:
            self.assertIn('Cluster', group.columns)
        
        # Check that kmeans_grp was populated
        self.assertEqual(len(kmeans_grp), 2)
        
        # Check that the DataFrames contain the expected POC IDs
        self.assertTrue(all(poc_id in result[0]['POC_ID'].values for poc_id in [1, 2, 3]))
        self.assertTrue(all(poc_id in result[1]['POC_ID'].values for poc_id in [4, 5]))

    # Additional test methods for edge cases would be implemented here
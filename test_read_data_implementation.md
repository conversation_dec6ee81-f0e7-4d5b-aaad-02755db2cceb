# Test Implementation for `read_data` Function

## Overview
This document outlines the implementation of tests for the `read_data` function that will be extracted from `Code/Accelerate.py` and modularized.

## Test File Structure
The test file will be named `test_read_data.py` and placed in the `tests/` directory, following the existing pattern of test files in the project.

## Implementation Plan

### File Header and Imports
```python
import unittest
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add the Code directory to the path so we can import from data_processing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Code'))
```

### Function Definition for Testing
Initially, we'll define the `read_data` function directly in the test file for testing purposes:
```python
def read_data(Base_data, meta):
    """
    Process and merge Base_data and meta DataFrames.
    
    Args:
        Base_data (pandas.DataFrame): Base data containing POC_SAPID column
        meta (pandas.DataFrame): Meta data containing store information
        
    Returns:
        list: List containing processed DATA and META DataFrames
    """
    DATA = Base_data    # From the Previous Module
    nd_cols = [x.replace(' ', '_') for x in DATA.columns]
    DATA.columns = nd_cols
    # print(DATA.columns.tolist())
    DATA = DATA.rename(columns={'POC_SAPID':'POC_ID'})
    DATA = DATA.dropna(subset=['POC_ID'])
    DATA.POC_ID = DATA.POC_ID.astype(int)
    DATA.POC_ID = DATA.POC_ID.astype(str)
    META=meta
     
    META = META.rename(columns={'Store number':'POC_ID','START DATE':'Start_Date','END DATE':'End_Date','TestvControl':'Test_Control'})
    META = META.drop_duplicates(subset=['POC_ID', 'Start_Date', 'End_Date'])
    print("Hi")
    print(META['Test_Control'].unique())
    # META.to_excel(E_path+"\\Meta_Test.xlsx")
    META['Start_Date'] = pd.to_datetime(META['Start_Date'], dayfirst=True)
    META['End_Date'] = pd.to_datetime(META['End_Date'], dayfirst=True)
    nd_cols = [x.replace(' ', '_') for x in META.columns]
    META.columns = nd_cols
    META = META.dropna(subset=['POC_ID'])
    META.POC_ID = META.POC_ID.astype(int)
    META.POC_ID = META.POC_ID.astype(str)
     
    try:
        DATA = pd.merge(DATA, META[['POC_ID','Start_Date','End_Date','Test_Control']], on=['POC_ID'], how='right') #,'End_Date'
        d_cols = list(DATA)
        DATA.columns = d_cols
    except Exception as e:
        print("Error in mapping Activation Date to Volume-Data", e)
    return [DATA,META]
```

### Test Class Definition
```python
class TestReadDataFunction(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
```

### Test Cases Implementation

#### 1. Normal Case Test
```python
    def test_normal_case(self):
        """Test with valid Base_data and meta DataFrames with all expected columns."""
        # Create test Base_data DataFrame
        base_data = {
            'POC_SAPID': [1001, 1002, 1003],
            'Value': [100, 200, 300],
            'Other_Column': ['A', 'B', 'C']
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create test meta DataFrame
        meta_data = {
            'Store number': [1001, 1002, 1004],
            'START DATE': ['01/01/2023', '01/02/2023', '01/03/2023'],
            'END DATE': ['01/31/2023', '02/28/2023', '03/31/2023'],
            'TestvControl': ['Test', 'Control', 'Test']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)  # DATA
        self.assertIsInstance(result[1], pd.DataFrame)  # META
        
        # Check that column names are properly formatted
        self.assertIn('POC_ID', result[0].columns)
        self.assertIn('POC_ID', result[1].columns)
        
        # Check that the merge worked correctly (right join)
        # Should contain POC_IDs from meta: 1001, 1002, 1004
        self.assertIn('1001', result[0]['POC_ID'].values)
        self.assertIn('1002', result[0]['POC_ID'].values)
        self.assertIn('1004', result[0]['POC_ID'].values)
```

#### 2. Empty DataFrames Test
```python
    def test_empty_dataframes(self):
        """Test with empty Base_data and meta DataFrames."""
        # Create empty DataFrames with expected column structure
        Base_data = pd.DataFrame(columns=['POC_SAPID', 'Value', 'Other_Column'])
        meta = pd.DataFrame(columns=['Store number', 'START DATE', 'END DATE', 'TestvControl'])
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)
        self.assertIsInstance(result[1], pd.DataFrame)
        
        # Column names should still be properly formatted
        self.assertIn('POC_ID', result[0].columns)
        self.assertIn('POC_ID', result[1].columns)
```

#### 3. Missing Columns Test
```python
    def test_missing_columns(self):
        """Test with DataFrames missing expected columns."""
        # Create Base_data missing 'POC_SAPID' column
        base_data = {
            'Value': [100, 200, 300],
            'Other_Column': ['A', 'B', 'C']
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create meta missing 'Store number' column
        meta_data = {
            'START DATE': ['01/01/2023', '01/02/2023'],
            'END DATE': ['01/31/2023', '02/28/2023'],
            'TestvControl': ['Test', 'Control']
        }
        meta = pd.DataFrame(meta_data)
        
        # This should raise a KeyError when trying to access missing columns
        with self.assertRaises(KeyError):
            read_data(Base_data, meta)
```

#### 4. Null Values in POC_ID Test
```python
    def test_null_values_in_poc_id(self):
        """Test with DataFrames containing null values in POC_ID columns."""
        # Create Base_data with null values in POC_SAPID column
        base_data = {
            'POC_SAPID': [1001, None, 1003],
            'Value': [100, 200, 300],
            'Other_Column': ['A', 'B', 'C']
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create meta with null values in Store number column
        meta_data = {
            'Store number': [1001, None, 1004],
            'START DATE': ['01/01/2023', '01/02/2023', '01/03/2023'],
            'END DATE': ['01/31/2023', '02/28/2023', '03/31/2023'],
            'TestvControl': ['Test', 'Control', 'Test']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)
        self.assertIsInstance(result[1], pd.DataFrame)
        
        # Rows with null POC_ID should be dropped
        # So result should not contain any null POC_ID values
        self.assertFalse(result[0]['POC_ID'].isnull().any())
        self.assertFalse(result[1]['POC_ID'].isnull().any())
        
        # Should only contain POC_IDs from meta without nulls: 1001, 1004
        self.assertIn('1001', result[0]['POC_ID'].values)
        self.assertIn('1004', result[0]['POC_ID'].values)
        self.assertNotIn('1002', result[0]['POC_ID'].values)  # This was null
```

#### 5. Duplicate Entries Test
```python
    def test_duplicate_entries(self):
        """Test with DataFrames containing duplicate entries."""
        # Create Base_data
        base_data = {
            'POC_SAPID': [1001, 1002, 1003],
            'Value': [100, 200, 300],
            'Other_Column': ['A', 'B', 'C']
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create meta with duplicate rows
        meta_data = {
            'Store number': [1001, 1002, 1002, 1004], # 1002 is duplicated
            'START DATE': ['01/01/2023', '01/02/2023', '01/02/2023', '01/03/2023'],
            'END DATE': ['01/31/2023', '02/28/2023', '02/28/2023', '03/31/2023'],
            'TestvControl': ['Test', 'Control', 'Control', 'Test']  # Also duplicated
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)
        self.assertIsInstance(result[1], pd.DataFrame)
        
        # Duplicates should be removed based on POC_ID, Start_Date, and End_Date
        # Since we have identical values for the duplicate rows, only one should remain
        # Count occurrences of POC_ID 1002 in meta result
        poc_1002_count = (result[1]['POC_ID'] == '1002').sum()
        self.assertEqual(poc_1002_count, 1)
```

#### 6. Merge Edge Cases Test
```python
    def test_merge_edge_cases(self):
        """Test edge cases in the DataFrame merging process."""
        # Create Base_data with POC_IDs not present in meta
        base_data = {
            'POC_SAPID': [1001, 1002, 1005], # 1005 not in meta
            'Value': [100, 200, 500],
            'Other_Column': ['A', 'B', 'E']
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create meta with POC_IDs not present in Base_data
        meta_data = {
            'Store number': [1001, 1002, 1004], # 1004 not in Base_data
            'START DATE': ['01/01/2023', '01/02/2023', '01/03/2023'],
            'END DATE': ['01/31/2023', '02/28/2023', '03/31/2023'],
            'TestvControl': ['Test', 'Control', 'Test']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)
        self.assertIsInstance(result[1], pd.DataFrame)
        
        # Right join behavior: result should contain only POC_IDs from meta
        result_poc_ids = set(result[0]['POC_ID'].values)
        meta_poc_ids = {'1001', '1002', '1004'}
        self.assertEqual(result_poc_ids, meta_poc_ids)
        
        # POC_ID 1005 from Base_data should not be in the result
        self.assertNotIn('1005', result[0]['POC_ID'].values)
```

#### 7. Column Name Transformation Test
```python
    def test_column_name_transformation(self):
        """Test conversion of column names with spaces to underscores."""
        # Create Base_data with spaces in column names
        base_data = {
            'POC_SAPID': [1001, 1002],
            'Value Column': [100, 200],  # Space in column name
            'Other Column': ['A', 'B']    # Space in column name
        }
        Base_data = pd.DataFrame(base_data)
        
        # Create meta with spaces in column names
        meta_data = {
            'Store number': [1001, 1002],
            'START DATE': ['01/01/2023', '01/02/2023'],
            'END DATE': ['01/31/2023', '02/28/2023'],
            'TestvControl': ['Test', 'Control']
        }
        meta = pd.DataFrame(meta_data)
        
        # Call the function
        result = read_data(Base_data, meta)
        
        # Assertions
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        self.assertIsInstance(result[0], pd.DataFrame)
        self.assertIsInstance(result[1], pd.DataFrame)
        
        # After processing, column names should have underscores
        # Check Base_data columns
        self.assertIn('Value_Column', result[0].columns)  # Space replaced with underscore
        self.assertIn('Other_Column', result[0].columns)  # Space replaced with underscore
        
        # Check meta columns
        self.assertIn('Start_Date', result[1].columns)    # Renamed and space handled
        self.assertIn('End_Date', result[1].columns)      # Renamed and space handled
```

### Test Runner
```python
if __name__ == '__main__':
    unittest.main()
```

## Post-Implementation Updates
After the `read_data` function is moved to its permanent module:
1. Remove the local function definition from the test file
2. Import the function from its new module location
3. Update the import path as needed

## Dependencies
- pandas
- unittest
- sys
- os

## Expected Test Results
All tests should pass when the implementation is correct. Tests cover:
- Normal operation
- Edge cases with empty data
- Error handling for missing columns
- Proper handling of null values
- Deduplication of meta data
- Correct merge behavior
- Column name transformation
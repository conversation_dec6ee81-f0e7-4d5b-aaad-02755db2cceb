import unittest
from datetime import datetime
import sys
import os

# Add the Code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Code'))

from utils import find_date, get_data_indices_n_years, get_activation_week_index, get_end_week_index

class TestFindDate(unittest.TestCase):
    
    def test_find_date_regular_case(self):
        """Test find_date with regular dates"""
        min_date = datetime(2023, 1, 15)
        max_date = datetime(2023, 12, 25)
        result = find_date(min_date, max_date)
        self.assertEqual(result, ('2023-01-15', '2023-12-25'))
    
    def test_find_date_single_digit_month_day(self):
        """Test find_date with single digit month and day"""
        min_date = datetime(2023, 1, 5)
        max_date = datetime(2023, 12, 5)
        result = find_date(min_date, max_date)
        self.assertEqual(result, ('2023-01-05', '2023-12-05'))
    
    def test_find_date_month_boundary(self):
        """Test find_date with month boundary dates"""
        min_date = datetime(2023, 2, 28)
        max_date = datetime(2023, 3, 1)
        result = find_date(min_date, max_date)
        self.assertEqual(result, ('2023-02-28', '2023-03-01'))
    
    def test_find_date_year_boundary(self):
        """Test find_date with year boundary dates"""
        min_date = datetime(2022, 12, 31)
        max_date = datetime(2023, 1, 1)
        result = find_date(min_date, max_date)
        self.assertEqual(result, ('2022-12-31', '2023-01-01'))

class TestGetActivationWeekIndex(unittest.TestCase):
    
    def test_normal_case(self):
        """Test with a normal case where date exists in columns"""
        date = datetime(2023, 1, 15)
        columns = ['2023-01-01', '2023-01-08', '2023-01-15', '2023-01-22']
        result = get_activation_week_index(date, columns)
        self.assertEqual(result, 2)
    
    def test_date_not_found(self):
        """Test with a date that doesn't exist in columns - should raise ValueError"""
        date = datetime(2023, 1, 15)
        columns = ['2023-01-01', '2023-01-08', '2023-01-22', '2023-01-29']
        with self.assertRaises(ValueError):
            get_activation_week_index(date, columns)
    
    def test_empty_columns(self):
        """Test with empty columns list - should raise ValueError"""
        date = datetime(2023, 1, 15)
        columns = []
        with self.assertRaises(ValueError):
            get_activation_week_index(date, columns)
    
    def test_first_occurrence(self):
        """Test with duplicate dates - should return index of first occurrence"""
        date = datetime(2023, 1, 15)
        columns = ['2023-01-01', '2023-01-15', '2023-01-08', '2023-01-15']
        result = get_activation_week_index(date, columns)
        self.assertEqual(result, 1)  # First occurrence at index 1
    
    def test_boundary_cases(self):
        """Test boundary cases"""
        date = datetime(2023, 1, 15)
        
        # Date at beginning of list
        columns = ['2023-01-15', '2023-01-22', '2023-01-29']
        result = get_activation_week_index(date, columns)
        self.assertEqual(result, 0)
        
        # Date at end of list
        columns = ['2023-01-01', '2023-01-08', '2023-01-15']
        result = get_activation_week_index(date, columns)
        self.assertEqual(result, 2)
        
        # Single element list
        columns = ['2023-01-15']
        result = get_activation_week_index(date, columns)
        self.assertEqual(result, 0)

class TestGetEndWeekIndex(unittest.TestCase):
    
    def test_normal_case(self):
        """Test with a normal case where date exists in columns"""
        date = datetime(2023, 1, 22)
        columns = ['2023-01-01', '2023-01-08', '2023-01-15', '2023-01-22']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 3)
    
    def test_date_not_found(self):
        """Test with a date that doesn't exist in columns - should raise ValueError"""
        date = datetime(2023, 1, 22)
        columns = ['2023-01-01', '2023-01-08', '2023-01-15', '2023-01-29']
        with self.assertRaises(ValueError) as context:
            get_end_week_index(date, columns)
        self.assertIn("not found in test_data_columns", str(context.exception))
    
    def test_empty_columns(self):
        """Test with empty columns list - should raise ValueError"""
        date = datetime(2023, 1, 22)
        columns = []
        with self.assertRaises(ValueError) as context:
            get_end_week_index(date, columns)
        self.assertIn("cannot be empty", str(context.exception))
    
    def test_invalid_date_object(self):
        """Test with invalid date object - should raise TypeError"""
        date = "2023-01-22"  # String instead of datetime object
        columns = ['2023-01-01', '2023-01-08', '2023-01-15', '2023-01-22']
        with self.assertRaises(TypeError) as context:
            get_end_week_index(date, columns)
        self.assertIn("must be a datetime object", str(context.exception))
    
    def test_invalid_columns_type(self):
        """Test with invalid columns type - should raise TypeError"""
        date = datetime(2023, 1, 22)
        columns = "2023-01-01, 2023-01-08, 2023-01-15, 2023-01-22"  # String instead of list
        with self.assertRaises(TypeError) as context:
            get_end_week_index(date, columns)
        self.assertIn("must be a list", str(context.exception))
    
    def test_first_occurrence(self):
        """Test with duplicate dates - should return index of first occurrence"""
        date = datetime(2023, 1, 22)
        columns = ['2023-01-01', '2023-01-22', '2023-01-08', '2023-01-22']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 1)  # First occurrence at index 1
    
    def test_boundary_cases(self):
        """Test boundary cases"""
        date = datetime(2023, 1, 22)
        
        # Date at beginning of list
        columns = ['2023-01-22', '2023-01-29', '2023-02-05']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 0)
        
        # Date at end of list
        columns = ['2023-01-01', '2023-01-08', '2023-01-22']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 2)
        
        # Single element list
        columns = ['2023-01-22']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 0)
    
    def test_different_date_formats(self):
        """Test with different valid datetime objects"""
        # Test with datetime at year boundary
        date = datetime(2023, 12, 31)
        columns = ['2023-12-24', '2023-12-31', '2024-01-07']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 1)
        
        # Test with datetime at month boundary
        date = datetime(2023, 2, 28)
        columns = ['2023-02-21', '2023-02-28', '2023-03-07']
        result = get_end_week_index(date, columns)
        self.assertEqual(result, 1)

class TestGetDataIndicesNYears(unittest.TestCase):
    
    def test_mixed_date_non_date_columns(self):
        """Test with mixed date and non-date columns"""
        columns = ['POC_ID', '2023-01-15', 'Start_Date', '2023-12-25', 'End_Date']
        result = get_data_indices_n_years(columns)
        # Expected: index_min=1 (2023-01-15), index_max=3 (2023-12-25), min_year=2023, max_year=2023
        self.assertEqual(result, (1, 3, 2023, 2023))
    
    def test_all_date_columns(self):
        """Test with all date columns"""
        columns = ['2022-06-01', '2023-01-15', '2023-12-25', '2024-03-10']
        result = get_data_indices_n_years(columns)
        # Expected: index_min=0 (2022-06-01), index_max=3 (2024-03-10), min_year=2022, max_year=2024
        self.assertEqual(result, (0, 3, 2022, 2024))
    
    def test_no_date_columns(self):
        """Test with no date columns - should raise ValueError"""
        columns = ['POC_ID', 'Start_Date', 'End_Date', 'Test_Control']
        with self.assertRaises(ValueError) as context:
            get_data_indices_n_years(columns)
        self.assertIn("No valid date columns found in the format YYYY-MM-DD", str(context.exception))
    
    def test_single_date_column(self):
        """Test with only one date column"""
        columns = ['POC_ID', '2023-07-04', 'Test_Control']
        result = get_data_indices_n_years(columns)
        # Expected: index_min=1, index_max=1 (same column), min_year=2023, max_year=2023
        self.assertEqual(result, (1, 1, 2023, 2023))
    
    def test_duplicate_dates(self):
        """Test with duplicate dates - should return last occurrence"""
        columns = ['POC_ID', '2023-01-15', 'Start_Date', '2023-01-15', '2023-12-25']
        result = get_data_indices_n_years(columns)
        # Expected: index_min=3 (last occurrence of 2023-01-15), index_max=4 (2023-12-25), min_year=2023, max_year=2023
        self.assertEqual(result, (3, 4, 2023, 2023))
    
    def test_invalid_date_formats(self):
        """Test with various invalid date formats - should be skipped"""
        columns = ['POC_ID', '2023/01/15', '01-15-2023', 'Start_Date', '2023-12-25', 'End_Date']
        result = get_data_indices_n_years(columns)
        # Only '2023-12-25' is valid, so both min and max should point to it
        self.assertEqual(result, (4, 4, 2023, 2023))
    
    def test_year_boundaries(self):
        """Test with dates spanning multiple years"""
        columns = ['2021-12-31', 'POC_ID', '2022-01-01', '2023-06-15', '2024-01-01']
        result = get_data_indices_n_years(columns)
        # Expected: index_min=0 (2021-12-31), index_max=4 (2024-01-01), min_year=2021, max_year=2024
        self.assertEqual(result, (0, 4, 2021, 2024))
    
    def test_empty_list(self):
        """Test with empty list - should raise ValueError"""
        columns = []
        with self.assertRaises(ValueError) as context:
            get_data_indices_n_years(columns)
        self.assertIn("No valid date columns found in the format YYYY-MM-DD", str(context.exception))

if __name__ == '__main__':
    unittest.main()
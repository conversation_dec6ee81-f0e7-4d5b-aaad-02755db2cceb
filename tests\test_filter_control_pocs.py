import unittest
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error
import math
import sys
import os

# Add the Code directory to the path so we can import from data_processing.py
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Code'))

from data_processing import filter_control_pocs

class TestFilterControlPocsFunction(unittest.TestCase):
    
    def setUp(self):
        """Set up test data for each test case."""
        pass
    
    def tearDown(self):
        """Clean up after each test case."""
        pass
    
    def test_normal_case(self):
        """Test with standard input data."""
        # Create test global_ctrl DataFrame
        ctrl_data = {
            'POC_ID': ['1001', '1002', '1003'],
            'col1': [10, 20, 30],
            'col2': [15, 25, 35],
            'col3': [12, 22, 32],
            'col4': [18, 28, 38]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        # Create test global_test DataFrame
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17],
            'col3': [14],
            'col4': [20]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters
        min_index = 1  # Start of baseline period
        activation_on = 3  # Activation date index
        limit = 2  # Limit for number of control POCs
        
        # Call the function
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        self.assertIn('RMSE Value', result.columns)
        self.assertIn('RMSE_Rank', result.columns)
        self.assertLessEqual(len(result), limit)
        # Verify that only POC_IDs from the original control are in the result
        self.assertTrue(all(poc_id in global_ctrl['POC_ID'].tolist() for poc_id in result['POC_ID'].tolist()))
    
    def test_empty_dataframe(self):
        """Test behavior with empty input."""
        # Create empty DataFrames
        global_ctrl = pd.DataFrame()
        global_test = pd.DataFrame()
        
        # Set parameters
        min_index = 0
        activation_on = 1
        limit = 5
        
        # Expect an exception or graceful handling
        # Depending on how the function should behave with empty data
        try:
            result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
            # If it doesn't raise an exception, check the result
            self.assertIsInstance(result, pd.DataFrame)
        except Exception as e:
            # If it raises an exception, that's also valid behavior
            # Just make sure it's handled gracefully
            pass
    
    def test_missing_columns(self):
        """Test behavior with missing columns."""
        # Create DataFrames with missing columns
        ctrl_data = {
            'POC_ID': ['1001', '1002']
            # Missing data columns
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001']
            # Missing data columns
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters
        min_index = 1
        activation_on = 2
        limit = 5
        
        # Expect an exception due to missing columns
        with self.assertRaises(Exception):
            result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    def test_mismatched_indices(self):
        """Test behavior with mismatched indices."""
        # Create DataFrames with mismatched indices
        ctrl_data = {
            'POC_ID': ['1001', '1002'],
            'col1': [10, 20],
            'col2': [15, 25]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17],
            'col3': [14]  # Extra column
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters that would cause index issues
        min_index = 1
        activation_on = 4  # This would be out of bounds for global_ctrl
        limit = 5
        
        # Expect an exception due to index out of bounds
        with self.assertRaises(Exception):
            result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    def test_limit_greater_than_controls(self):
        """Test with limit greater than number of control POCs."""
        # Create test DataFrames
        ctrl_data = {
            'POC_ID': ['1001', '1002', '1003'],
            'col1': [10, 20, 30],
            'col2': [15, 25, 35]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters with limit greater than number of controls
        min_index = 1
        activation_on = 3
        limit = 10  # Greater than number of control POCs (3)
        
        # Call the function
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        # Should return all control POCs since limit is greater than available
        self.assertEqual(len(result), len(global_ctrl))
    
    def test_limit_less_than_controls(self):
        """Test with limit less than number of control POCs."""
        # Create test DataFrames
        ctrl_data = {
            'POC_ID': ['1001', '1002', '1003', '1004', '1005'],
            'col1': [10, 20, 30, 40, 50],
            'col2': [15, 25, 35, 45, 55]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters with limit less than number of controls
        min_index = 1
        activation_on = 3
        limit = 3  # Less than number of control POCs (5)
        
        # Call the function
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        # Should return only the specified limit number of control POCs
        self.assertEqual(len(result), limit)
    
    def test_nan_values(self):
        """Test with NaN values in data."""
        # Create test DataFrames with NaN values
        ctrl_data = {
            'POC_ID': ['1001', '1002', '1003'],
            'col1': [10, np.nan, 30],
            'col2': [15, 25, np.nan]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters
        min_index = 1
        activation_on = 3
        limit = 5
        
        # Expect an exception due to NaN values not being handled by mean_squared_error
        with self.assertRaises(ValueError):
            result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
    
    def test_single_control_poc(self):
        """Test with single control POC."""
        # Create test DataFrames with single control POC
        ctrl_data = {
            'POC_ID': ['1001'],
            'col1': [10],
            'col2': [15]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [12],
            'col2': [17]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters
        min_index = 1
        activation_on = 3
        limit = 5
        
        # Call the function
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 1)
        self.assertEqual(result['POC_ID'].iloc[0], '1001')
    
    def test_identical_data(self):
        """Test with identical test and control data."""
        # Create test DataFrames with identical data
        ctrl_data = {
            'POC_ID': ['1001', '1002'],
            'col1': [10, 10],
            'col2': [15, 15]
        }
        global_ctrl = pd.DataFrame(ctrl_data)
        
        test_data = {
            'POC_ID': ['2001'],
            'col1': [10],
            'col2': [15]
        }
        global_test = pd.DataFrame(test_data)
        
        # Set parameters
        min_index = 1
        activation_on = 3
        limit = 5
        
        # Call the function
        result = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)
        
        # Assertions
        self.assertIsInstance(result, pd.DataFrame)
        # With identical data, RMSE should be 0 for all controls
        self.assertTrue(all(result['RMSE Value'] == 0))

if __name__ == '__main__':
    unittest.main()
# Plan for Moving `significance_level` Function

## Overview
This document outlines the plan for moving the `significance_level` function from `Code/Accelerate.py` to `Code/analysis.py`.

## Dependencies Analysis
The `significance_level` function depends on:
1. `scipy.stats.wilcoxon` - Statistical test function
2. `pandas` - DataFrame operations
3. Built-in `print` function for debugging
4. DataFrame indexing and filtering operations

## Steps for Moving the Function

### 1. Update `Code/analysis.py`
- Add import for `from scipy.stats import wilcoxon` if not already present
- Add the `significance_level` function with proper documentation
- Ensure the function signature remains the same for backward compatibility

### 2. Update `Code/Accelerate.py`
- Remove the `significance_level` function definition
- Update the import statement to include `significance_level` from `analysis`
- Ensure all calls to the function continue to work as before

### 3. Required Changes in `Code/analysis.py`

Add the import:
```python
from scipy.stats import wilcoxon
```

Add the function with documentation:
```python
def significance_level(data, APT_RESULTS, sku):
    """
    Calculate statistical significance level using <PERSON><PERSON> test.
    
    This function filters outliers and null values from the input data,
    performs a Wilcoxon test on two samples, and calculates the significance level.
    The result is added to the APT_RESULTS DataFrame.
    
    Parameters:
    data (pd.DataFrame): Input data containing outlier information and lift values
    APT_RESULTS (pd.DataFrame): Results DataFrame to be updated with significance values
    sku (int or str): SKU identifier for filtering results
    
    Returns:
    float: Significance level as a percentage
    
    Notes:
    - Filters data where Outlier == "No" and ABI-% Validation Period Lift is not null
    - Uses ABI-% Validation Period Lift and ABI-%Lift columns for comparison
    - Performs Wilcoxon test and calculates significance as (1 - p) * 100
    - Updates APT_RESULTS DataFrame with significance value for matching SKUs
    """
    # Filter outliers & nulls
    APT_RESULTS_NO_OUTLIER = data[
        (data["Outlier"] == "No") &
        ~(data["ABI-% Validation Period Lift"].isnull())
    ]
    
    # Prepare the two samples
    data1 = APT_RESULTS_NO_OUTLIER["ABI-% Validation Period Lift"]
    data2 = APT_RESULTS_NO_OUTLIER["ABI-%Lift"]

    print(f"Running Wilcoxon test for SKU: {repr(sku)}")
    print(f"Sample sizes → data1: {len(data1)}, data2: {len(data2)}")
    
    # Compare samples
    stat, p = wilcoxon(data1, data2)
    print(f"Statistics={stat:.3f}, p={p:.3f}")
    
    significance_val = (1 - p) * 100
    print(f"Significance level = {significance_val:.2f}%")
    
    # Create the column if it doesn't exist
    if 'Significance' not in APT_RESULTS.columns:
        APT_RESULTS['Significance'] = None
    
    # Assign in place
    mask = APT_RESULTS['SKU'].astype(int) == int(sku)
    APT_RESULTS.loc[mask, 'Significance'] = significance_val

    # Check result
    print(APT_RESULTS.loc[mask, ['SKU', 'Significance']])
    
    # Interpret result
    alpha = 0.05
    if p > alpha:
        print('Same distribution (fail to reject H0)')
    else:
        print('Different distribution (reject H0)')

    return significance_val
```

### 4. Required Changes in `Code/Accelerate.py`

Update the import statement from:
```python
from analysis import testvscontrolfcn, get_clustered_data, get_dist_mat_grp, get_optimal_n_cluster, get_uplift
```

To:
```python
from analysis import testvscontrolfcn, get_clustered_data, get_dist_mat_grp, get_optimal_n_cluster, get_uplift, significance_level
```

Remove the function definition from the file.

## Verification Steps
1. Ensure all imports are correct
2. Verify that the function works identically after the move
3. Run existing tests to ensure no regressions
4. Test the specific functionality with sample data
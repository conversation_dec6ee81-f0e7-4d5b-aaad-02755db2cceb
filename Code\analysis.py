import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
import time
from dtaidistance import dtw
from sklearn.metrics import silhouette_score
import warnings
from numpy import inf
from scipy.stats import wilcoxon
import os

from utils import get_activation_week_index, get_end_week_index

def compare_test_control(control_data, test_data):
    """
    Compare test and control data by computing means of numeric columns.
    
    This function processes control and test DataFrames by:
    1. Dropping known non-numeric or irrelevant columns
    2. Replacing 0 values with NaN
    3. Selecting only numeric columns
    4. Computing means for each group
    5. Combining and returning the results with appropriate labels
    
    Parameters:
    control_data (pd.DataFrame): DataFrame containing control group data
    test_data (pd.DataFrame): DataFrame containing test group data
    
    Returns:
    pd.DataFrame: A DataFrame with two rows (Test and Control) containing
                  the mean values for each numeric column
    """
    # Drop known non-numeric or irrelevant columns
    drop_columns = ['Start_Date', 'End_Date', 'NET_AVG_Y1', 'POC_ID']
    control_data = control_data.drop(columns=[col for col in drop_columns if col in control_data.columns])
    test_data = test_data.drop(columns=[col for col in drop_columns if col in test_data.columns])
    
    # Replace 0 with NaN
    control_data = control_data.replace(0, np.nan)
    test_data = test_data.replace(0, np.nan)
    
    # Select only numeric columns (to avoid TypeError)
    control_data = control_data.select_dtypes(include='number')
    test_data = test_data.select_dtypes(include='number')
    
    # Compute means
    test_mean = test_data.mean(axis=0, skipna=True).to_frame().transpose()
    control_mean = control_data.mean(axis=0, skipna=True).to_frame().transpose()
    
    # Align column order (optional but safe)
    control_mean = control_mean[test_mean.columns]
    
    # Combine test and control means
    test_control_mean = pd.concat([test_mean, control_mean], ignore_index=True)
    test_control_mean.rename(index={0: 'Test', 1: 'Control'}, inplace=True)
    
    return test_control_mean


def get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA):
    index = 0
    vol_grp_cnt = 1
    clustered_data_grp = []
    for _dm in dist_mat_grp:
        t1 = time.time()
        kmeans = KMeans(n_clusters=num_clusters_grp[index], random_state=47).fit(_dm)
        kmeans_grp.append(kmeans)
        _labels = kmeans.predict(_dm)
        
        # Filter data by POC IDs for this group
        _data = DATA[DATA.POC_ID.isin(poc_ids_grp[index])]
        
        # Only add cluster labels if there is data to cluster
        if not _data.empty:
            _data = _data.copy()  # Create a copy to avoid SettingWithCopyWarning
            _data['Cluster'] = _labels.tolist()
        else:
            # If no data, create an empty DataFrame with Cluster column
            _data = pd.DataFrame(columns=list(DATA.columns) + ['Cluster'])
            
        clustered_data_grp.append(_data)
        index = index + 1
        vol_grp_cnt = vol_grp_cnt + 1
    return clustered_data_grp


def get_dist_mat_grp(dm_data_grp):
    """
    Calculate distance matrices for groups of data using Dynamic Time Warping (DTW).
    
    This function processes a list of DataFrames, calculating a distance matrix for each
    using DTW. NaN and infinite values in the resulting matrices are replaced with 0.
    
    Parameters:
    dm_data_grp (list): List of pandas DataFrames containing numeric data for distance calculation
    
    Returns:
    list: List of numpy arrays representing distance matrices for each input DataFrame
    
    Notes:
    - Uses a fixed random seed (47) for reproducible results
    - Replaces NaN and infinite values with 0
    - Prints timing information for each group processed
    """
    vol_grp_cnt = 1
    dist_mat_grp = []
    dm_data_len = len(dm_data_grp)
    
    for dm_vg in dm_data_grp:
        try:
            t1 = time.time()
            np.random.seed(47)
            _dm = dtw.distance_matrix(dm_vg.values, parallel=False)

            _nan_val = np.isnan(_dm)
            _inf_val = np.isinf(_dm)

            _dm[_nan_val] = 0
            _dm[_inf_val] = 0

            dist_mat_grp.append(_dm)

            _disp_msg = "[DM] Took {} seconds [Volume-Group-" + str(vol_grp_cnt) + "]"
            print(_disp_msg.format(time.time() - t1))
            vol_grp_cnt = vol_grp_cnt + 1
        except Exception as e:
            print(e)
            print("Error in calculating distance matrix for Group-" + str(vol_grp_cnt))
            vol_grp_cnt = vol_grp_cnt + 1
            
    return dist_mat_grp


def get_optimal_n_cluster(dist_mat_grp):
    """
    Determine the optimal number of clusters for each distance matrix using Silhouette Coefficient.
    
    This function processes a list of distance matrices, determining the optimal number of clusters
    for each using the Silhouette Coefficient method. For each distance matrix, it tries clustering
    with 1-2 clusters and selects the number that yields the highest Silhouette Coefficient.
    
    Parameters:
    dist_mat_grp (list): List of numpy arrays representing distance matrices
    
    Returns:
    list: List of integers representing optimal cluster counts for each input matrix
    
    Notes:
    - Uses a fixed random seed (47) for reproducible results
    - Prints Silhouette Coefficient values for each cluster count tested
    - Prints timing information for each group processed
    - Handles exceptions during clustering gracefully
    """
    vol_grp_cnt = 1
    num_clusters_grp = []
    dist_mat_len = len(dist_mat_grp)

    for _di in range(0,dist_mat_len):
        t1 = time.time()
        max_sil_score = -1
        opt_clus = 1

        X = dist_mat_grp[_di]
        #decide min/max no of clusters
        for n_cluster in range(1, 3):
            try:
                kmeans = KMeans(n_clusters=n_cluster,random_state=47).fit(X)
                label = kmeans.labels_
                sil_coeff = silhouette_score(X, label, metric='euclidean')

                if sil_coeff > max_sil_score:
                    max_sil_score = sil_coeff
                    opt_clus = n_cluster

                print("\tFor n_clusters={}, The Silhouette Coefficient is {}".format(n_cluster, sil_coeff))
            except:
                print("\tError in finding optimal cluster for Group-"+str(1+_di))

        #num_clusters_grp.append(opt_clus+1)
        num_clusters_grp.append(opt_clus)

        _disp_msg = "[Volume-Group-"+str(vol_grp_cnt)+"] | Optimal n-Cluster = "+str(opt_clus)+" | Took {} seconds"
        print(_disp_msg.format(time.time() - t1))
        print("---------------------------------------------------------------------------")
        vol_grp_cnt = vol_grp_cnt + 1
    return num_clusters_grp


def get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end, test_poc,
               ctrl_pocs, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku):
    """
    Calculate uplift metrics by comparing test and control data.
    
    This function calculates various metrics to determine the effectiveness of a campaign
    by comparing test site performance with control group performance. It computes baseline
    and analysis period metrics for both groups, calculates percentage increases, and derives
    uplift metrics. The results are updated in the provided APT_RESULTS DataFrame.
    
    Parameters:
    desc_test (pd.Series): Test data series
    desc_ctrl (pd.Series): Control data series
    min_index (int): Minimum index for data range
    activation_on (int): Activation start index
    activation_end (int): Activation end index
    test_poc (int): Test POC ID
    ctrl_pocs (list): Control POC IDs
    ctrl_outliers (int): Number of control outliers
    RESTRICT_BASELINE_TO (int): Baseline restriction period
    APT_RESULTS (pd.DataFrame): Results DataFrame to update
    campaign (str): Campaign identifier
    retailer (str): Retailer information
    sku (int): SKU information
    
    Returns:
    list: A list containing the following metrics:
        - perc_inc_t: Test percentage increase
        - perc_inc_c: Control percentage increase
        - lift: Calculated lift
        - impact: Calculated impact
        - test_expected: Expected test value
        - _avg_vol: Average volume
    """
    activation_on = activation_on - min_index
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    
    # Initialize return values with default values
    perc_inc_t = 0
    perc_inc_c = 0
    lift = 0
    impact = 0
    test_expected = 0
    _avg_vol = 0
    
    if len(desc_test) == len(desc_ctrl):
        # Handle empty series case
        if len(desc_test) == 0 or len(desc_ctrl) == 0:
            try:
                APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Campaign'] = campaign
                APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Retailer'] = retailer
                APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'SKU'] = sku
            except Exception as e:
                print("Error updating APT_RESULTS for empty series:", e)
            return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]
        
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0
        baseline_start = 0
        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO
            
        # Ensure we don't go out of bounds
        baseline_end = min(activation_on, len(desc_test), len(desc_ctrl))
        baseline_start = max(0, baseline_start)
        
        for nRec in range(baseline_start, baseline_end):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1
                
        analysis_end_index = min((activation_end - min_index) + 1, len(desc_test), len(desc_ctrl))  # -3 because 3 columns excluded, poc_id, DATE, End_Date
        analysis_start_index = max(activation_on, 0)
        
        # Ensure we don't go out of bounds
        analysis_end_index = min(analysis_end_index, len(desc_test), len(desc_ctrl))
        analysis_start_index = max(0, analysis_start_index)
        
        for nRec in range(analysis_start_index, analysis_end_index):
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1
                
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on] if activation_on > baseline_start else pd.Series([])
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index] if analysis_end_index > activation_on else pd.Series([])
        desc_test_1 = desc_test[baseline_start:activation_on] if activation_on > baseline_start else pd.Series([])
        desc_test_2 = desc_test[activation_on:analysis_end_index] if analysis_end_index > activation_on else pd.Series([])

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
        
        # Avoid division by zero
        if (ba_count - ctrl_zero_y1) != 0:
            avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1)
        else:
            avg_ctrl_y1 = 0
            
        if (ba_count - test_zero_y1) != 0:
            avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1)
        else:
            avg_test_y1 = 0
        
        if (aa_count - ctrl_zero_y2) != 0:
            avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2)
        else:
            avg_ctrl_y2 = 0
            
        if (aa_count - test_zero_y2) != 0:
            avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2)
        else:
            avg_test_y2 = 0
              
        # Avoid division by zero
        if avg_test_y1 != 0:
            perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1
        else:
            perc_inc_t = 0
            
        if avg_ctrl_y1 != 0:
            perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1
        else:
            perc_inc_c = 0
        
        if perc_inc_c != 0:
            test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        else:
            test_expected = avg_test_y1
            
        if test_expected != 0:
            lift = 100 * (avg_test_y2 - test_expected) / test_expected
        else:
            lift = 0
            
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0] if not desc_ctrl_1.empty else 0
        as_mths = desc_ctrl_2.shape[0] if not desc_ctrl_2.empty else 0
        
        if np.isinf(lift):
            lift = 0
            
        # Handle empty series for mean calculation
        if len(desc_test) > (activation_end - activation_on + 1) and (activation_end - activation_on + 1) > 0:
            desc_test_avg = (desc_test[:-(activation_end - activation_on + 1)].mean())
        else:
            desc_test_avg = 0
            
        if len(desc_ctrl) > (activation_end - activation_on + 1) and (activation_end - activation_on + 1) > 0:
            desc_ctrl_avg = (desc_ctrl[:-(activation_end - activation_on + 1)].mean())
        else:
            desc_ctrl_avg = 0
            
        desc_test_dev = []
        desc_ctrl_dev = []
        
        for j in range(len(desc_test)):
            if desc_test_avg != 0:
                desc_test_dev.append((desc_test[j] - desc_test_avg) / desc_test_avg)
            else:
                desc_test_dev.append(0)
                
        for j in range(len(desc_ctrl)):
            if desc_ctrl_avg != 0:
                desc_ctrl_dev.append((desc_ctrl[j] - desc_ctrl_avg) / desc_ctrl_avg)
            else:
                desc_ctrl_dev.append(0)

        # Calculate diff and score
        diff = [abs(desc_test_dev[k] - desc_ctrl_dev[k]) for k in range(min(len(desc_test_dev), len(desc_ctrl_dev)))]
        if len(diff) > (activation_end - activation_on + 1) and (activation_end - activation_on + 1) > 0:
            score = sum(diff[:-(activation_end - activation_on + 1)])
        else:
            score = sum(diff) if diff else 0
        
        try:
            # APT_RESULTS.loc[APT_RESULTS['POC_ID'] == test_poc, 'ABI-TEST AVG VOL'] = (avg_test_y1+avg_test_y2)/2 #desc_test.mean()
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-TEST AVG VOL'] = (avg_test_y1 + avg_test_y2) / 2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-AVG VOL'] = _avg_vol
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Lift'] = lift
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Estimated impact'] = impact
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test baseline period'] = avg_test_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test analysis period'] = avg_test_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Test expected'] = test_expected
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Test performance'] = perc_inc_t
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control baseline period'] = avg_ctrl_y1
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control analysis period'] = avg_ctrl_y2
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-%Control performance'] = perc_inc_c
        
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Baseline period # weeks with data'] = bs_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Analysis period # weeks with data'] = as_mths
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control count'] = len(ctrl_pocs)
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Control Outliers'] = ctrl_outliers
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Goodness of fit score'] = score
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Campaign'] = campaign
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'Retailer'] = retailer
            APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'SKU'] = sku
        except Exception as e:
            print("Error after Seven:", e)
        # print(APT_RESULTS.head())
    else:
        print("ERROR : Test and Control are not having same number of columns")
    # print(perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol)
    
    return [perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol]


def significance_level(data, APT_RESULTS, sku):
    """
    Calculate significance level using Wilcoxon test.
    
    This function filters outliers and nulls from the data, prepares two samples,
    runs a Wilcoxon test on the samples, calculates a significance value, updates
    the APT_RESULTS DataFrame with the significance value, and returns the significance value.
    
    Parameters:
    data (pd.DataFrame): Input data containing outlier information and lift values
    APT_RESULTS (pd.DataFrame): Results DataFrame to update with significance values
    sku (int or str): SKU identifier for updating the correct rows in APT_RESULTS
    
    Returns:
    float: Significance value as a percentage
    """
    # Filter outliers & nulls
    APT_RESULTS_NO_OUTLIER = data[
        (data["Outlier"] == "No") &
        ~(data["ABI-% Validation Period Lift"].isnull())
    ]
    
    # Prepare the two samples
    data1 = APT_RESULTS_NO_OUTLIER["ABI-% Validation Period Lift"]
    data2 = APT_RESULTS_NO_OUTLIER["ABI-%Lift"]

    print(f"Running Wilcoxon test for SKU: {repr(sku)}")
    print(f"Sample sizes → data1: {len(data1)}, data2: {len(data2)}")
    
    # Initialize variables
    stat = np.nan
    p = np.nan
    significance_val = 0.0
    
    # Handle case where there's no data
    if len(data1) > 0 and len(data2) > 0:
        # Compare samples
        stat, p = wilcoxon(data1, data2)
        print(f"Statistics={stat:.3f}, p={p:.3f}")
        
        significance_val = (1 - p) * 100
    else:
        print(f"Statistics={stat}, p={p}")
        print(f"Significance level = {significance_val:.2f}%")
    
    # Create the column if it doesn't exist
    if 'Significance' not in APT_RESULTS.columns:
        APT_RESULTS['Significance'] = None
    
    # Assign in place
    mask = APT_RESULTS['SKU'].astype(int) == int(sku)
    APT_RESULTS.loc[mask, 'Significance'] = significance_val

    # Check result
    print(APT_RESULTS.loc[mask, ['SKU', 'Significance']])
    
    # Interpret result
    alpha = 0.05
    if p > alpha:
        print('Same distribution (fail to reject H0)')
    else:
        print('Different distribution (reject H0)')

    return significance_val


def lift_outlier_iqr(data):
    """
    Identify outliers in the results data based on various criteria.
    
    This function applies outlier detection based on a fixed threshold (300 for upper bound,
    -300 for lower bound) on the 'ABI-%Lift' column and checks for various other conditions
    that would mark a row as an outlier.
    
    Parameters:
    data (pd.DataFrame): Input DataFrame containing results data
    
    Returns:
    pd.DataFrame: Modified DataFrame with 'Outlier' and 'Outlier_Reason' columns added
    
    Outlier criteria:
    1. 'ABI-%Lift' values beyond thresholds (300/-300)
    2. NaN values in key columns:
       - 'ABI-Test analysis period'
       - 'ABI-Control analysis period'
       - 'ABI-Test baseline period'
       - 'ABI-Control baseline period'
       - 'ABI-Control count'
    3. Negative values in key columns
    4. 'ABI-Control count' below 5
    """
    upper_bound = 300
    lower_bound = -300
    
    data['Outlier'] = data['ABI-%Lift'].apply(lambda x: "Yes" if (x> upper_bound or x< lower_bound) else "No")
    data['Outlier_Reason'] = data['ABI-%Lift'].apply(lambda x: "Uplift beyond threshold" if (x> upper_bound or x<lower_bound) else "")
    
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test analysis period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the analysis period"
    
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control analysis period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the analysis period"

    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test baseline period'].isna()), 'Outlier_Reason'] = "Test site has zero-valued in the baseline period"
    
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control baseline period'].isna()), 'Outlier_Reason'] = "Control site has zero-valued in the baseline period"

    data.loc[(data['ABI-Test analysis period']<0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test analysis period']<0), 'Outlier_Reason'] = "Test site has negative data in the analysis period"
    
    data.loc[(data['ABI-Control analysis period']<0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control analysis period']<0), 'Outlier_Reason'] = "Control site has negative data in the analysis period"

    data.loc[(data['ABI-Test baseline period']<0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Test baseline period']<0), 'Outlier_Reason'] = "Test site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control baseline period']<0), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control baseline period']<0), 'Outlier_Reason'] = "Control site has negative data in the baseline period"
    
    data.loc[(data['ABI-Control count'].isna()), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control count'].isna()), 'Outlier_Reason'] = "Test site has control POCs below 5"
    
    data.loc[(data['ABI-Control count']<5), 'Outlier'] = "Yes"
    data.loc[(data['ABI-Control count']<5), 'Outlier_Reason'] = "Test site has control POCs below 5"

    return data


def get_uplift_val(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, test_poc, ctrl_pocs, RESTRICT_BASELINE_TO, APT_RESULTS):
    """
    Calculate uplift validation metrics by comparing test and control data.
    
    This function calculates validation period uplift metrics by comparing test site
    performance with control group performance during a validation period. It computes
    baseline and validation period metrics for both groups, calculates percentage
    increases, and derives uplift metrics. The results are updated in the provided
    APT_RESULTS DataFrame.
    
    Parameters:
    desc_test (pd.Series): Test data series
    desc_ctrl (pd.Series): Control data series
    min_index (int): Minimum index for data range
    activation_on (int): Activation start index
    validation_start (int): Validation period start index
    validation_end (int): Validation period end index
    test_poc (int): Test POC ID
    ctrl_pocs (list): Control POC IDs
    RESTRICT_BASELINE_TO (int): Baseline restriction period
    APT_RESULTS (pd.DataFrame): Results DataFrame to update
    
    Returns:
    list: A list containing the calculated lift value
    """
    # Initialize lift to 0
    lift = 0
    
    if isinstance(test_poc, pd.Series) or isinstance(test_poc, np.ndarray):
        test_poc = int(test_poc.iloc[0] if hasattr(test_poc, 'iloc') else test_poc[0])
    activation_on = activation_on - min_index
    if len(desc_test) == len(desc_ctrl):
        nRecords = len(desc_test)
        sum_ctrl_y1 = 0
        sum_ctrl_y2 = 0
        sum_test_y1 = 0
        sum_test_y2 = 0
        
        ctrl_zero_y1 = 0
        ctrl_zero_y2 = 0
        test_zero_y1 = 0
        test_zero_y2 = 0
        
        baseline_start = 0
        if activation_on > RESTRICT_BASELINE_TO:
            baseline_start = activation_on - RESTRICT_BASELINE_TO
        for nRec in range(baseline_start, activation_on):
            sum_ctrl_y1 = sum_ctrl_y1 + desc_ctrl[nRec]
            sum_test_y1 = sum_test_y1 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y1 = ctrl_zero_y1 + 1
            if (desc_test[nRec] == 0):
                test_zero_y1 = test_zero_y1 + 1
        analysis_end_index = (validation_end - min_index) + 1  # -3 because 3 columns exculded,poc_id,DATE,End_Date
        
        for nRec in range(activation_on, analysis_end_index):
            # print("NREC......................",nRec)
            sum_ctrl_y2 = sum_ctrl_y2 + desc_ctrl[nRec]
            sum_test_y2 = sum_test_y2 + desc_test[nRec]
            if (desc_ctrl[nRec] == 0):
                ctrl_zero_y2 = ctrl_zero_y2 + 1
            if (desc_test[nRec] == 0):
                test_zero_y2 = test_zero_y2 + 1
        
        # print(desc_ctrl)
        desc_ctrl_1 = desc_ctrl[baseline_start:activation_on]
        desc_ctrl_2 = desc_ctrl[activation_on:analysis_end_index]
        desc_test_1 = desc_test[baseline_start:activation_on]
        desc_test_2 = desc_test[activation_on:analysis_end_index]

        ba_count = activation_on - baseline_start
        aa_count = analysis_end_index - activation_on
               
        avg_ctrl_y1 = sum_ctrl_y1 / (ba_count - ctrl_zero_y1)
        avg_test_y1 = sum_test_y1 / (ba_count - test_zero_y1)
        
        avg_ctrl_y2 = sum_ctrl_y2 / (aa_count - ctrl_zero_y2)
        avg_test_y2 = sum_test_y2 / (aa_count - test_zero_y2)
              
        perc_inc_t = 100 * (avg_test_y2 - avg_test_y1) / avg_test_y1
        perc_inc_c = 100 * (avg_ctrl_y2 - avg_ctrl_y1) / avg_ctrl_y1
        
        test_expected = avg_test_y1 * (1 + (perc_inc_c / 100))
        lift = 100 * (avg_test_y2 - test_expected) / test_expected
        impact = avg_test_y2 - test_expected
               
        _avg_vol = (avg_test_y1 + avg_test_y2 + avg_ctrl_y1 + avg_ctrl_y2) / 4
        bs_mths = desc_ctrl_1.shape[0]
        as_mths = desc_ctrl_2.shape[0]
        

        if np.isinf(lift):
            lift = 0
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-% Validation Period Lift'] = lift
        APT_RESULTS.loc[APT_RESULTS['POC_ID'].astype(str) == str(test_poc), 'ABI-Validation Period Impact'] = impact
        
    else:
        print("ERROR : Test and Control are not having same number of columns")
    
    return [lift]


def process_uplift_calculation(data_live, test_live, DATA, META, min_index, max_index, activity_id, sku, E_path, campaign, retailer):
    """Process uplift calculation through clustering and analysis"""
    from utils import get_activation_week_index, get_end_week_index
    from data_processing import filter_control_pocs
    
    percentile_grp = [0]
    percentile_values_old = []
    percentile_values = []
    data_vol_grp = []
    poc_ids_grp = []
    date_grp = []
    dm_data_grp = []
    dist_mat_grp = []
    num_clusters_grp = []
    kmeans_grp = []
    clustered_data_grp = []
    run_test_pocs = []
    run_ctrl_pocs = []
    
    for p in percentile_grp:
        _q = np.percentile(test_live.NET_AVG_Y1, p)
        percentile_values_old.append(_q)
    a = percentile_values_old
    
    t1 = (data_live['NET_AVG_Y1']).sort_values().reset_index().drop(columns='index')
    _l = -1
    _r = -1
    data_vol_grp = []
    for pv in percentile_values:
        _r = pv
        if _l == -1:
            data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] < _r])
        else:
            data_vol_grp.append(data_live[(data_live['NET_AVG_Y1'] < _r) & (data_live['NET_AVG_Y1'] >= _l)])
        _l = _r
    data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] >= _r])
    
    data_vol_grp_copy = data_vol_grp
    dm_data_grp_split = []
    max_poc_count = 2000
    min_poc_count = 200
    for i in range(len(data_vol_grp_copy)):
        if (len(data_vol_grp_copy[i]) <= max_poc_count):
            dm_data_grp_split.append(data_vol_grp_copy[i])
        else:
            tot = int(np.ceil(len(data_vol_grp_copy[i])/max_poc_count))
            min_row_range =0
            for j in range(tot):
                max_row_range = min_row_range + max_poc_count
                if max_row_range > len(data_vol_grp_copy[i]):
                    max_row_range = len(data_vol_grp_copy[i])
                if (len(data_vol_grp_copy[i]) - max_row_range) < min_poc_count:
                    max_row_range = len(data_vol_grp_copy[i])
                    dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                    break
                dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                min_row_range = max_row_range
    
    _t_upd = []
    vol_grp_cnt = 1
    for vg in dm_data_grp_split:
        _t = vg.reset_index(drop = True)
        _t_upd.append(_t)
    dm_data_grp_split = _t_upd
    
    poc_ids_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['POC_ID']
        poc_ids_grp.append(_poc_id)
    
    date_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['Start_Date']
        date_grp.append(_poc_id)
    
    dm_data_grp = []
    for vg in dm_data_grp_split:
        _t = vg
        numeric_cols = _t.select_dtypes(include=[np.number]).columns
        _t_numeric = _t[numeric_cols]
        _max_val = _t_numeric.max()
        _t_numeric = _t_numeric.div(_max_val, axis=0)
        _t[numeric_cols] = _t_numeric
        dm_data_grp.append(_t)
    
    dist_mat_grp = get_dist_mat_grp(dm_data_grp)
    num_clusters_grp = get_optimal_n_cluster(dist_mat_grp)
    clustered_data_grp = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA)
    
    total_test_pocs = DATA[DATA['Test_Control']=='Test'].POC_ID.tolist()
    print(len(total_test_pocs))
    data_len = len(clustered_data_grp)
    test_pocs_len = len(total_test_pocs)

    # Initialize APT_RESULTS properly from META data like in original
    APT_RESULTS = META.copy()
    APT_RESULTS = APT_RESULTS[APT_RESULTS.POC_ID.isin(total_test_pocs)]
    APT_RESULTS["Start_Date"] = pd.to_datetime(APT_RESULTS["Start_Date"])
    APT_RESULTS["End_Date"] = pd.to_datetime(APT_RESULTS["End_Date"])
    APT_RESULTS.drop(['Start_Date', 'End_Date'], axis=1, inplace=True)

    test_control_list = pd.DataFrame()
    RESTRICT_BASELINE_TO = 12

    # Save clustered data like in original
    if len(clustered_data_grp) > 0:
        clustered_data_grp[0]['Cluster'] = 0
        try:
            clustered_data_grp[0].to_excel(E_path+"\\Clustered_Data"+f"\\clustered_group_0_{activity_id}_{sku}.xlsx", index=False)
        except:
            pass  # Directory might not exist

    i = 0
    limit = 5
    for test_poc in total_test_pocs:
        i = i + 1
        if(i % 100 == 0):
            print("Test POC No:", i)
        for di in range(0, data_len):
            g = clustered_data_grp[di]
            if (len(g[g.POC_ID==test_poc]) > 0):
                global_test = g[g.POC_ID == str(test_poc)]

                # Skip if global_test is empty
                if global_test.empty:
                    continue

                global_ctrl = g[g.Test_Control=='Control']

                if len(global_ctrl) > 0:
                    ctrl_len_before_filter = len(global_ctrl)
                    global_test["Start_Date"] = pd.to_datetime(global_test["Start_Date"])
                    global_test["End_Date"] = pd.to_datetime(global_test["End_Date"])
                    activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
                    activation_end = get_end_week_index(global_test.End_Date.iloc[0], list(global_test))

                    if len(global_ctrl) > limit:
                        global_ctrl = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, limit)

                    ctrl_len_after_filter = len(global_ctrl)
                    ctrl_outliers = ctrl_len_before_filter - ctrl_len_after_filter

                    # Calculate validation period
                    if((activation_end - activation_on) + 1 > 12):
                        validation_period = 2
                    else:
                        validation_period = 1
                    validation_start = activation_on - validation_period
                    validation_end = activation_on

                    global_ctrl_test = global_ctrl.copy()
                    global_ctrl_test['Test_POC_ID'] = test_poc
                    control_list = global_ctrl_test[["Test_POC_ID","POC_ID"]]
                    test_control_list = pd.concat([test_control_list, control_list])

                    desc_test = global_test[global_test.columns[np.concatenate([range(min_index,max_index+1)])]].mean()
                    desc_ctrl = global_ctrl[global_ctrl.columns[np.concatenate([range(min_index,max_index+1)])]].mean()

                    perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol = get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end, global_test.POC_ID, global_ctrl.POC_ID, ctrl_outliers, RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku)

                    validation_lift = get_uplift_val(desc_test, desc_ctrl, min_index, activation_on, validation_start, validation_end, global_test.POC_ID, global_ctrl.POC_ID, RESTRICT_BASELINE_TO, APT_RESULTS)

    # Process test_control_list like in original code
    col = ['Campaign_ID','Test_Control','Start_Date','End_Date']
    print("XYZ")
    print(META.columns.tolist())
    temp = META.drop(col, axis=1)
    print("dropped")

    try:
        test_control_list = test_control_list.rename(columns={'POC_ID': 'Control_POC_ID'})
        test_control_list_temp = test_control_list.merge(temp, left_on='Test_POC_ID', right_on='POC_ID', how='left')
        test_control_list_temp = test_control_list_temp.rename(columns={'GEOGRAPHY_DESCRIPTION':'Test_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Test_Store_No'})
        test_control_list_final = test_control_list_temp.merge(temp, left_on='Control_POC_ID', right_on='POC_ID', how='left')
        test_control_list_final = test_control_list_final.rename(columns={'GEOGRAPHY_DESCRIPTION_y':'Control_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Control_Store_No'})
        test_control_list_final = test_control_list_final.drop(['POC_ID_x','POC_ID_y'],axis=1)
        test_control_list = test_control_list_final
    except Exception as e:
        print(f"Error processing test_control_list: {e}")
        pass

    # Save test control list
    try:
        test_control_list.to_excel(E_path+"\\TestvCtrl"+f"\\{activity_id}.xlsx", sheet_name="Control_Mapping", index=False)
    except Exception as e:
        print(f"Error saving test_control_list: {e}")
        pass

    return test_control_list, APT_RESULTS

    """
    Perform data analysis and clustering operations.
    
    This function handles the complete data analysis and clustering workflow including:
    1. Data preprocessing and grouping
    2. Distance matrix calculation
    3. Optimal cluster determination
    4. Data clustering
    5. Post-processing of clustered data
    6. Control group filtering and uplift calculation
    
    Parameters:
    controlled (pd.DataFrame): Controlled data
    test (pd.DataFrame): Test data
    min_index (int): Minimum index for data range
    max_index (int): Maximum index for data range
    DATA (pd.DataFrame): Main data
    META (pd.DataFrame): Metadata
    activity_id (str): Activity identifier
    sku (str): SKU identifier
    E_path (str): Base path for file operations
    campaign (str): Campaign identifier
    retailer (str): Retailer information
    
    Returns:
    tuple: (test_control_list, APT_RESULTS)
    """
    # Initialize variables
    percentile_grp = [0]
    percentile_values_old = []
    percentile_values = []
    data_vol_grp = []
    poc_ids_grp = []
    date_grp = []
    dm_data_grp = []
    dist_mat_grp = []
    num_clusters_grp = []
    kmeans_grp = []
    clustered_data_grp = []
    run_test_pocs = []
    run_ctrl_pocs = []
    
    data_live = pd.concat([controlled, test])
    data_live = data_live.sort_values(by=['NET_AVG_Y1'])
    test_live = test.sort_values(by=['NET_AVG_Y1'])
    data_live = data_live[data_live.NET_AVG_Y1 != 0]
    test_live = test_live[test_live.NET_AVG_Y1 != 0]
    
    for p in percentile_grp:
        _q = np.percentile(test_live.NET_AVG_Y1, p)
        percentile_values_old.append(_q)
    a = percentile_values_old
    from operator import add
    t1 = (data_live['NET_AVG_Y1']).sort_values().reset_index().drop(columns='index')
    _l = -1
    _r = -1
    data_vol_grp = []
    for pv in percentile_values:
        _r = pv
        if _l == -1:
            data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] < _r])
        else:
            data_vol_grp.append(data_live[(data_live['NET_AVG_Y1'] < _r) & (data_live['NET_AVG_Y1'] >= _l)])
        _l = _r
    data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] >= _r])
    data_vol_grp_copy = data_vol_grp
    dm_data_grp_split = []
    max_poc_count = 2000
    min_poc_count = 200
    for i in range(len(data_vol_grp_copy)):
        if (len(data_vol_grp_copy[i]) <= max_poc_count):
            dm_data_grp_split.append(data_vol_grp_copy[i])
        else:
            tot = int(np.ceil(len(data_vol_grp_copy[i])/max_poc_count))
            min_row_range =0
            for j in range(tot):
                max_row_range = min_row_range + max_poc_count
                if max_row_range > len(data_vol_grp_copy[i]):
                    max_row_range = len(data_vol_grp_copy[i])
                if (len(data_vol_grp_copy[i]) - max_row_range) < min_poc_count:
                    max_row_range = len(data_vol_grp_copy[i])
                    dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                    break
                dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                min_row_range = max_row_range
    dm_data_grp_split = data_vol_grp
    _t_upd = []
    vol_grp_cnt = 1
    for vg in dm_data_grp_split:
        _t = vg.reset_index(drop = True)
        _t_upd.append(_t)
    dm_data_grp_split = _t_upd
    poc_ids_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['POC_ID']
        poc_ids_grp.append(_poc_id)
    date_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['Start_Date']
        date_grp.append(_poc_id)
    dm_data_grp = []
    for vg in dm_data_grp_split:
        _t = vg
        # Select only numeric columns for normalization
        numeric_cols = _t.select_dtypes(include=[np.number]).columns
        _t_numeric = _t[numeric_cols]
        _max_val = _t_numeric.max()
        _t_numeric = _t_numeric.div(_max_val, axis=0)
        _t[numeric_cols] = _t_numeric
        dm_data_grp.append(_t)
    percentile_values=[]
    kmeans_grp=[]
    dist_mat_grp = get_dist_mat_grp(dm_data_grp)
    # print(dist_mat_grp)
    # with open(path+"\\test_v4.txt", "wb") as fp:   #Pickling
        # pickle.dump(dist_mat_grp, fp)
    num_clusters_grp = []
    num_clusters_grp = get_optimal_n_cluster(dist_mat_grp)
    clustered_data_grp = []
    # print(DATA.head())
    print(DATA['Test_Control'].unique())
    # DATA.to_excel(E_path,"\\Data_check.xlsx")
    clustered_data_grp = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp,kmeans_grp,DATA)
    total_test_pocs = DATA[DATA['Test_Control']=='Test'].POC_ID.tolist()
    print(len(total_test_pocs))
    data_len = len(clustered_data_grp)
    test_pocs_len = len(total_test_pocs)
    APT_RESULTS=META.copy()
    APT_RESULTS=APT_RESULTS[APT_RESULTS.POC_ID.isin(total_test_pocs)]
    APT_RESULTS["Start_Date"]=pd.to_datetime(APT_RESULTS["Start_Date"])
    APT_RESULTS["End_Date"]=pd.to_datetime(APT_RESULTS["End_Date"])
    APT_RESULTS.drop(['Start_Date', 'End_Date'], axis=1, inplace=True)
    # print(META.head())
    # print("APT_RESULTS")
    # print(APT_RESULTS.head())
    clustered_data_grp[0].Cluster = 0
    clustered_data_grp[0]
    clustered_data_grp[0].to_excel(E_path+"\\Clustered_Data"+f"\\clustered_group_0_{activity_id}_{sku}.xlsx", index=False)
    i=0
    limit = 5
    test_control_list = pd.DataFrame()
    RESTRICT_BASELINE_TO = 12
    for test_poc in total_test_pocs:
        i=i+1
        if(i%100==0):
            print("Test POC No:",i)
        for di in range(0,data_len):
            g = clustered_data_grp[di]
            # print(g['Test_Control'].unique())
            if (len(g[g.POC_ID==test_poc])>0):
                global_test = g[g.POC_ID == str(test_poc)]
                global_ctrl = g[g.Test_Control=='Control']
                # print(len(global_ctrl))
                if len(global_ctrl) > 0:
                    ctrl_len_before_filter = len(global_ctrl)
                    global_test["Start_Date"]=pd.to_datetime(global_test["Start_Date"])
                    global_test["End_Date"]=pd.to_datetime(global_test["End_Date"])
                    from utils import get_activation_week_index, get_end_week_index
                    activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
                    activation_end=get_end_week_index(global_test.End_Date.iloc[0], list(global_test))
                    if len(global_ctrl) > limit:
                        from data_processing import filter_control_pocs
                        global_ctrl = filter_control_pocs(global_ctrl, global_test,min_index,activation_on,limit)
                    ctrl_len_after_filter = len(global_ctrl)
                    ctrl_outliers = ctrl_len_before_filter - ctrl_len_after_filter
                    if((activation_end - activation_on) + 1> 12):
                        validation_period = 2
                    else:
                        validation_period = 1
                    validation_start = activation_on - validation_period
                    validation_end = activation_on
                    global_ctrl_test = global_ctrl
                    global_ctrl_test['Test_POC_ID'] = test_poc
                    control_list = global_ctrl_test[["Test_POC_ID","POC_ID"]]
                    test_control_list = pd.concat([test_control_list, control_list])
                    desc_test = global_test[global_test.columns[np.concatenate([range(min_index,max_index+1)])]].mean()
                    desc_ctrl = global_ctrl[global_ctrl.columns[np.concatenate([range(min_index,max_index+1)])]].mean()
                    # print("ABC")
                    perc_inc_t, perc_inc_c, lift, impact, test_expected, _avg_vol = get_uplift(desc_test,desc_ctrl,min_index,activation_on,activation_end,global_test.POC_ID, global_ctrl.POC_ID,ctrl_outliers,RESTRICT_BASELINE_TO,APT_RESULTS,campaign,retailer,sku)
                    # print("GHI")
                    validation_lift= get_uplift_val(desc_test, desc_ctrl,min_index,activation_on,validation_start,validation_end,global_test.POC_ID, global_ctrl.POC_ID,RESTRICT_BASELINE_TO,APT_RESULTS)
    col= ['Campaign_ID','Test_Control','Start_Date','End_Date']
    print("XYZ")
    print(META.columns.tolist())
    temp=META.drop(col, axis=1)
    print("dropped")
    # ['Campaign_ID', 'POC_ID', 'Test_Control', 'Start_Date', 'End_Date']
    try:
        test_control_list = test_control_list.rename(columns={'POC_ID': 'Control_POC_ID'})
        test_control_list_temp = test_control_list.merge(temp, left_on='Test_POC_ID', right_on='POC_ID', how='left')
        test_control_list_temp = test_control_list_temp.rename(columns={'GEOGRAPHY_DESCRIPTION':'Test_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Test_Store_No'})
        test_control_list_final = test_control_list_temp.merge(temp, left_on='Control_POC_ID', right_on='POC_ID', how='left')
        test_control_list_final = test_control_list_final.rename(columns={'GEOGRAPHY_DESCRIPTION_y':'Control_GEOGRAPHY_DESCRIPTION','STORE_NO': 'Control_Store_No'})
        test_control_list_final= test_control_list_final.drop(['POC_ID_x','POC_ID_y'],axis=1)
        test_control_list = test_control_list_final
    except:
        pass
    
    return test_control_list, APT_RESULTS


def perform_data_analysis_and_clustering(controlled, test, min_index, max_index, DATA, META, activity_id, sku, E_path, campaign, retailer):
    """Perform data analysis and clustering operations."""
    # Initialize variables
    percentile_grp = [0]
    percentile_values_old = []
    percentile_values = []
    data_vol_grp = []
    poc_ids_grp = []
    date_grp = []
    dm_data_grp = []
    dist_mat_grp = []
    num_clusters_grp = []
    kmeans_grp = []
    clustered_data_grp = []
    run_test_pocs = []
    run_ctrl_pocs = []
    
    data_live = pd.concat([controlled, test])
    data_live = data_live.sort_values(by=['NET_AVG_Y1'])
    test_live = test.sort_values(by=['NET_AVG_Y1'])
    data_live = data_live[data_live.NET_AVG_Y1 != 0]
    test_live = test_live[test_live.NET_AVG_Y1 != 0]
    
    for p in percentile_grp:
        _q = np.percentile(test_live.NET_AVG_Y1, p)
        percentile_values_old.append(_q)
    a = percentile_values_old
    
    t1 = (data_live['NET_AVG_Y1']).sort_values().reset_index().drop(columns='index')
    _l = -1
    _r = -1
    data_vol_grp = []
    for pv in percentile_values:
        _r = pv
        if _l == -1:
            data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] < _r])
        else:
            data_vol_grp.append(data_live[(data_live['NET_AVG_Y1'] < _r) & (data_live['NET_AVG_Y1'] >= _l)])
        _l = _r
    data_vol_grp.append(data_live[data_live['NET_AVG_Y1'] >= _r])
    
    data_vol_grp_copy = data_vol_grp
    dm_data_grp_split = []
    max_poc_count = 2000
    min_poc_count = 200
    for i in range(len(data_vol_grp_copy)):
        if (len(data_vol_grp_copy[i]) <= max_poc_count):
            dm_data_grp_split.append(data_vol_grp_copy[i])
        else:
            tot = int(np.ceil(len(data_vol_grp_copy[i])/max_poc_count))
            min_row_range =0
            for j in range(tot):
                max_row_range = min_row_range + max_poc_count
                if max_row_range > len(data_vol_grp_copy[i]):
                    max_row_range = len(data_vol_grp_copy[i])
                if (len(data_vol_grp_copy[i]) - max_row_range) < min_poc_count:
                    max_row_range = len(data_vol_grp_copy[i])
                    dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                    break
                dm_data_grp_split.append(data_vol_grp_copy[i].iloc[min_row_range:max_row_range,:])
                min_row_range = max_row_range
    
    _t_upd = []
    vol_grp_cnt = 1
    for vg in dm_data_grp_split:
        _t = vg.reset_index(drop = True)
        _t_upd.append(_t)
    dm_data_grp_split = _t_upd
    
    poc_ids_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['POC_ID']
        poc_ids_grp.append(_poc_id)
    
    date_grp = []
    for vg in dm_data_grp_split:
        _poc_id = vg['Start_Date']
        date_grp.append(_poc_id)
    
    dm_data_grp = []
    for vg in dm_data_grp_split:
        _t = vg
        numeric_cols = _t.select_dtypes(include=[np.number]).columns
        _t_numeric = _t[numeric_cols]
        _max_val = _t_numeric.max()
        _t_numeric = _t_numeric.div(_max_val, axis=0)
        _t[numeric_cols] = _t_numeric
        dm_data_grp.append(_t)
    
    dist_mat_grp = get_dist_mat_grp(dm_data_grp)
    num_clusters_grp = get_optimal_n_cluster(dist_mat_grp)
    clustered_data_grp = get_clustered_data(dist_mat_grp, num_clusters_grp, date_grp, poc_ids_grp, kmeans_grp, DATA)
    
    total_test_pocs = DATA[DATA['Test_Control']=='Test'].POC_ID.tolist()
    data_len = len(clustered_data_grp)
    test_pocs_len = len(total_test_pocs)
    
    APT_RESULTS = pd.DataFrame()
    test_control_list = pd.DataFrame()
    RESTRICT_BASELINE_TO = 12
    
    for test_poc in total_test_pocs:
        for di in range(0,data_len):
            g = clustered_data_grp[di]
            if (len(g[g.POC_ID==test_poc])>0):
                global_test = g[g.POC_ID == str(test_poc)]
                global_ctrl = g[g.Test_Control=='Control']
                
                if len(global_ctrl) > 0:
                    ctrl_len_before_filter = len(global_ctrl)
                    global_test["Start_Date"] = pd.to_datetime(global_test["Start_Date"])
                    activation_on = get_activation_week_index(global_test.Start_Date.iloc[0], list(global_test))
                    activation_end = get_end_week_index(global_test.End_Date.iloc[0], list(global_test))
                    
                    if len(global_ctrl) > 5:
                        global_ctrl = filter_control_pocs(global_ctrl, global_test, min_index, activation_on, 5)
                    
                    ctrl_outliers = ctrl_len_before_filter - len(global_ctrl)
                    global_ctrl_test = global_ctrl.copy()
                    global_ctrl_test['Test_POC_ID'] = test_poc
                    control_list = global_ctrl_test[["Test_POC_ID","POC_ID"]]
                    test_control_list = pd.concat([test_control_list, control_list])
                    
                    desc_test = global_test.iloc[:, min_index:max_index+1].mean()
                    desc_ctrl = global_ctrl.iloc[:, min_index:max_index+1].mean()
                    
                    get_uplift(desc_test, desc_ctrl, min_index, activation_on, activation_end,
                              global_test.POC_ID, global_ctrl.POC_ID, ctrl_outliers,
                              RESTRICT_BASELINE_TO, APT_RESULTS, campaign, retailer, sku)
    
    return test_control_list, APT_RESULTS